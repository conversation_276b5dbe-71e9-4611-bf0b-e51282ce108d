# 高考成绩单图片识别系统 v2.0

这是一个基于OCR技术的高考成绩单图片识别系统，能够自动识别图片中的个人信息和成绩数据，并导出为Excel文件。

## ✨ 功能特性

### 🔍 智能识别
- **高精度OCR**: 使用PaddleOCR识别中文文字，支持多种图片格式
- **智能预处理**: 自动图片去噪、对比度增强、锐化处理
- **多模式匹配**: 支持多种成绩单格式的自动识别
- **容错处理**: 自动重试机制，提高识别成功率

### 📊 数据提取
- **个人信息**: 姓名、性别、身份证号、考生号、证书编号
- **成绩信息**: 语文、数学、外语、历史、思想政治、地理、总分
- **上下文分析**: 智能分析文字位置关系，提高提取准确性
- **配置化模式**: 支持自定义识别模式和正则表达式

### ✅ 数据验证
- **分数验证**: 自动验证总分是否等于各科成绩之和
- **格式检查**: 验证身份证号、考生号格式正确性
- **范围检查**: 检查分数是否在合理范围内
- **质量评估**: 对提取数据进行质量评分

### 📋 输出功能
- **Excel报告**: 生成包含数据和汇总的多工作表Excel文件
- **格式化输出**: 自动样式设置、列宽调整、条件格式
- **日志记录**: 详细的处理日志和错误信息
- **调试支持**: 导出OCR原始文本用于问题排查

### 🚀 性能优化
- **多线程处理**: 支持并行处理多张图片
- **进度显示**: 实时显示处理进度和预计剩余时间
- **内存优化**: 智能内存管理，支持大批量图片处理
- **配置灵活**: 可调整线程数、重试次数等参数

## 📁 项目结构

```
gaokaocj/
├── imgs/                    # 图片文件夹（放置要处理的图片）
├── output/                  # 输出文件夹（自动创建）
├── logs/                    # 日志文件夹（自动创建）
├── main.py                  # 主程序入口
├── run.py                   # 简化启动脚本
├── install.py               # 自动安装脚本
├── ocr_processor.py         # OCR图片识别模块
├── data_extractor.py        # 数据提取和验证模块
├── excel_exporter.py        # Excel导出模块
├── utils.py                 # 工具函数模块
├── config.json              # 配置文件
├── requirements.txt         # 项目依赖
└── README.md               # 项目说明
```

## 🚀 快速开始

### 方法一：自动安装（推荐）

```bash
# 1. 自动安装所有依赖
python install.py

# 2. 使用友好界面启动
python run.py
```

### 方法二：手动安装

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 直接运行主程序
python main.py
```

### 主要依赖包

- `paddlepaddle`: PaddlePaddle深度学习框架
- `paddleocr`: PaddleOCR文字识别库
- `pandas`: 数据处理库
- `openpyxl`: Excel文件处理库
- `Pillow`: 图片处理库
- `opencv-python`: 计算机视觉库
- `numpy`: 数值计算库

## 📖 使用指南

### 1. 准备图片

将要处理的高考成绩单图片放入 `imgs` 文件夹中：
- 支持格式：PNG (.png, .PNG)、JPG (.jpg, .JPG, .jpeg, .JPEG)
- 建议图片清晰度：300DPI以上
- 建议图片大小：不超过10MB

### 2. 运行程序

**使用简化界面（推荐）：**
```bash
python run.py
```

**直接运行主程序：**
```bash
python main.py
```

### 3. 查看结果

程序运行完成后，在 `output` 文件夹中查看：
- `高考成绩统计_YYYYMMDD_HHMMSS.xlsx`: 完整的Excel报告
- `OCR原始文本_YYYYMMDD_HHMMSS.txt`: OCR识别原始文本
- `processing_log_YYYYMMDD_HHMMSS.log`: 详细处理日志

## 输出说明

### Excel文件包含两个工作表：

#### 1. 成绩数据表
包含以下列：
- 图片文件名
- 姓名
- 性别
- 身份证号
- 考生号
- 证书编号
- 总分
- 语文
- 数学
- 外语
- 历史
- 思想政治
- 地理
- 计算总分
- 分数验证（通过/失败）
- 差值

#### 2. 数据汇总表
包含：
- 处理时间
- 总图片数
- 验证通过数量
- 验证失败数量
- 通过率
- 验证失败的文件列表

## 数据验证

程序会自动验证总分是否等于各科成绩之和：
- ✅ **验证通过**: 总分等于各科成绩之和
- ❌ **验证失败**: 总分不等于各科成绩之和，会显示差值

## 模块说明

### OCRProcessor (ocr_processor.py)
- 图片预处理（去噪、增强对比度）
- 使用PaddleOCR进行文字识别
- 提取文字位置和内容信息

### DataExtractor (data_extractor.py)
- 使用正则表达式提取结构化数据
- 支持多种字段格式的识别
- 数据清理和标准化
- 成绩验证功能

### ExcelExporter (excel_exporter.py)
- 生成格式化的Excel文件
- 支持多工作表导出
- 自动样式设置和列宽调整
- 数据汇总统计

### GaoKaoScoreProcessor (main.py)
- 主程序控制逻辑
- 批量图片处理
- 进度显示和错误处理
- 结果汇总和导出

## 注意事项

1. **图片质量**: 确保图片清晰，文字可读
2. **网络连接**: 首次运行时PaddleOCR会下载模型文件
3. **内存要求**: 处理大量图片时需要足够的内存
4. **文件权限**: 确保程序有读取imgs文件夹和写入output文件夹的权限

## 故障排除

### 常见问题

1. **OCR识别失败**
   - 检查图片是否清晰
   - 确认图片格式是否支持
   - 检查网络连接（下载模型）

2. **数据提取不准确**
   - 检查成绩单格式是否标准
   - 查看OCR原始文本文件确认识别结果
   - 可能需要调整正则表达式模式

3. **Excel导出失败**
   - 确认output文件夹存在且有写入权限
   - 检查是否有其他程序占用输出文件

## 开发和扩展

### 添加新的字段识别
在 `data_extractor.py` 的 `patterns` 字典中添加新的正则表达式模式。

### 自定义输出格式
修改 `excel_exporter.py` 中的 `columns` 列表和相关方法。

### 优化OCR识别
调整 `ocr_processor.py` 中的图片预处理参数。

## 许可证

本项目仅供学习和研究使用。

## 🧪 测试系统

运行系统测试以验证所有功能：

```bash
python test_system.py
```

## 📞 技术支持

### 常见问题

**Q: OCR识别准确率不高怎么办？**
A:
1. 确保图片清晰度足够（建议300DPI以上）
2. 调整配置文件中的预处理参数
3. 降低置信度阈值
4. 检查图片是否有旋转或倾斜

**Q: 程序运行很慢怎么办？**
A:
1. 增加配置文件中的线程数
2. 关闭不必要的图片预处理
3. 如果有GPU，启用GPU加速
4. 减少重试次数

**Q: 某些字段识别不到怎么办？**
A:
1. 查看OCR原始文本文件确认是否识别到相关文字
2. 在配置文件中添加新的正则表达式模式
3. 检查成绩单格式是否与预期不同

**Q: 分数验证总是失败怎么办？**
A:
1. 检查各科成绩是否都正确识别
2. 确认总分计算逻辑是否正确
3. 查看详细日志了解具体错误

### 获取帮助

1. 查看详细日志文件：`logs/processing_log_*.log`
2. 查看OCR原始文本：`output/OCR原始文本_*.txt`
3. 运行系统测试：`python test_system.py`
4. 查看配置说明：`配置说明.md`

## 📄 许可证

本项目仅供学习和研究使用。

## 🔄 更新日志

### v2.0.0 (2024-12-19)
- ✨ 新增配置化管理系统
- 🚀 支持多线程并行处理
- 📊 增强数据验证和质量检查
- 🎨 改进用户界面和进度显示
- 🔧 添加自动安装和测试脚本
- 📝 完善文档和配置说明
- 🛠️ 优化图片预处理算法
- 📋 增强Excel报告格式

### v1.0.0 (2024-12-19)
- 🎉 初始版本发布
- 🔍 支持基本的OCR识别和数据提取
- 📊 Excel导出功能
- ✅ 数据验证功能

---

**感谢使用高考成绩单图片识别系统！** 🎓
