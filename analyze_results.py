"""
分析处理结果
"""

import pandas as pd
import os


def analyze_latest_results():
    """分析最新的处理结果"""
    
    # 找到最新的Excel文件
    output_dir = "output"
    excel_files = [f for f in os.listdir(output_dir) if f.startswith("高考成绩统计_") and f.endswith(".xlsx")]
    
    if not excel_files:
        print("❌ 未找到Excel结果文件")
        return
    
    latest_excel = sorted(excel_files)[-1]
    excel_path = os.path.join(output_dir, latest_excel)
    
    print(f"分析文件: {excel_path}")
    print("="*60)
    
    try:
        # 读取成绩数据表
        df = pd.read_excel(excel_path, sheet_name="成绩数据")
        
        print("📊 数据概览:")
        print(f"总记录数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print()
        
        # 分析各字段的识别情况
        print("📋 字段识别统计:")
        fields = ['姓名', '性别', '身份证号', '考生号', '证书编号', '总分', '语文', '数学', '外语', '历史', '思想政治', '地理']
        
        for field in fields:
            if field in df.columns:
                # 统计非空且不是"未识别"的记录
                valid_count = len(df[(df[field].notna()) & (df[field] != '') & (df[field] != '未识别')])
                success_rate = (valid_count / len(df)) * 100
                
                status = "✅" if success_rate >= 80 else "⚠️" if success_rate >= 50 else "❌"
                print(f"  {status} {field}: {valid_count}/{len(df)} ({success_rate:.1f}%)")
        
        print()
        
        # 分析分数验证情况
        if '分数验证' in df.columns:
            validation_stats = df['分数验证'].value_counts()
            print("🔍 分数验证统计:")
            for status, count in validation_stats.items():
                percentage = (count / len(df)) * 100
                print(f"  {status}: {count} ({percentage:.1f}%)")
        
        print()
        
        # 显示前几条记录
        print("📄 前5条记录:")
        print("-"*60)
        
        # 选择关键字段显示
        key_fields = ['图片文件名', '身份证号', '考生号', '总分', '分数验证']
        display_fields = [f for f in key_fields if f in df.columns]
        
        if display_fields:
            for i, row in df.head().iterrows():
                print(f"记录 {i+1}:")
                for field in display_fields:
                    value = row[field] if pd.notna(row[field]) else "未识别"
                    print(f"  {field}: {value}")
                print()
        
        # 分析成功案例
        print("🎯 成功识别的案例:")
        print("-"*60)
        
        # 找到身份证号和总分都识别成功的记录
        successful_records = df[
            (df['身份证号'].notna()) & (df['身份证号'] != '') & (df['身份证号'] != '未识别') &
            (df['总分'].notna()) & (df['总分'] != '') & (df['总分'] != '未识别')
        ]
        
        if len(successful_records) > 0:
            print(f"找到 {len(successful_records)} 条成功识别的记录:")
            for i, row in successful_records.head(3).iterrows():
                print(f"  文件: {row['图片文件名']}")
                print(f"  身份证号: {row['身份证号']}")
                print(f"  考生号: {row.get('考生号', '未识别')}")
                print(f"  总分: {row['总分']}")
                print(f"  验证: {row.get('分数验证', '未知')}")
                print()
        else:
            print("未找到完全成功的记录")
        
        # 读取汇总表
        try:
            summary_df = pd.read_excel(excel_path, sheet_name="数据汇总")
            print("📈 汇总信息:")
            print("-"*60)
            
            # 显示汇总数据的前几行
            for i, row in summary_df.head(10).iterrows():
                if pd.notna(row.iloc[0]) and str(row.iloc[0]).strip():
                    if pd.notna(row.iloc[1]) and str(row.iloc[1]).strip():
                        print(f"  {row.iloc[0]}: {row.iloc[1]}")
                    else:
                        print(f"  {row.iloc[0]}")
        
        except Exception as e:
            print(f"无法读取汇总表: {str(e)}")
        
    except Exception as e:
        print(f"❌ 分析Excel文件失败: {str(e)}")


def compare_with_ocr_text():
    """对比OCR文本和提取结果"""
    print("\n" + "="*60)
    print("🔍 OCR文本对比分析")
    print("="*60)
    
    # 读取最新的OCR文本
    output_dir = "output"
    ocr_files = [f for f in os.listdir(output_dir) if f.startswith("OCR原始文本_")]
    
    if not ocr_files:
        print("❌ 未找到OCR文本文件")
        return
    
    latest_ocr_file = sorted(ocr_files)[-1]
    ocr_file_path = os.path.join(output_dir, latest_ocr_file)
    
    with open(ocr_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分析OCR识别的数字
    import re
    
    # 提取所有数字
    numbers = re.findall(r'\b\d+\b', content)
    
    # 分类数字
    id_numbers = [n for n in numbers if len(n) in [15, 17, 18]]
    exam_numbers = [n for n in numbers if len(n) in [10, 11, 12, 13, 14]]
    scores = [n for n in numbers if len(n) <= 3 and int(n) <= 750]
    
    print(f"OCR识别统计:")
    print(f"  身份证号候选: {len(id_numbers)} 个")
    print(f"  考生号候选: {len(exam_numbers)} 个")
    print(f"  分数候选: {len(scores)} 个")
    
    if scores:
        scores_int = [int(s) for s in scores]
        print(f"  分数范围: {min(scores_int)} - {max(scores_int)}")
        print(f"  可能的总分: {[s for s in scores_int if s > 200]}")


if __name__ == "__main__":
    analyze_latest_results()
    compare_with_ocr_text()
