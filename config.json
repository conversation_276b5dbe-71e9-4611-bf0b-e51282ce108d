{"ocr_settings": {"languages": ["ch_sim", "en"], "use_gpu": false, "confidence_threshold": 0.3, "enable_preprocessing": true, "paragraph": false, "width_ths": 0.7, "height_ths": 0.7}, "image_preprocessing": {"enable_denoising": true, "enable_contrast_enhancement": true, "enable_sharpening": true, "resize_factor": 1.0, "gaussian_blur_kernel": 3}, "extraction_patterns": {"name": ["姓\\s*名[：:：]\\s*([^\\s\\n]+)", "姓名\\s*[：:：]\\s*([^\\s\\n]+)", "考生姓名[：:：]\\s*([^\\s\\n]+)", "学生姓名[：:：]\\s*([^\\s\\n]+)"], "gender": ["性\\s*别[：:：]\\s*([男女])", "性别\\s*[：:：]\\s*([男女])"], "id_number": ["身份证号[：:：]\\s*([0-9X]{15,18})", "身份证号码[：:：]\\s*([0-9X]{15,18})", "证件号码[：:：]\\s*([0-9X]{15,18})", "身份证[：:：]\\s*([0-9X]{15,18})"], "exam_number": ["考生号[：:：]\\s*([0-9]{8,15})", "准考证号[：:：]\\s*([0-9]{8,15})", "考号[：:：]\\s*([0-9]{8,15})", "学号[：:：]\\s*([0-9]{8,15})"], "certificate_number": ["证书编号[：:：]\\s*([A-Z0-9\\-]+)", "证书号[：:：]\\s*([A-Z0-9\\-]+)", "编号[：:：]\\s*([A-Z0-9\\-]+)", "证件编号[：:：]\\s*([A-Z0-9\\-]+)"], "total_score": ["总\\s*分[：:：]\\s*([0-9]+)", "总分[：:：]\\s*([0-9]+)", "合计[：:：]\\s*([0-9]+)", "总成绩[：:：]\\s*([0-9]+)"], "chinese": ["语\\s*文[：:：]\\s*([0-9]+)", "语文[：:：]\\s*([0-9]+)"], "math": ["数\\s*学[：:：]\\s*([0-9]+)", "数学[：:：]\\s*([0-9]+)"], "foreign_language": ["外\\s*语[：:：]\\s*([0-9]+)", "外语[：:：]\\s*([0-9]+)", "英\\s*语[：:：]\\s*([0-9]+)", "英语[：:：]\\s*([0-9]+)"], "history": ["历\\s*史[：:：]\\s*([0-9]+)", "历史[：:：]\\s*([0-9]+)"], "politics": ["思想政治[：:：]\\s*([0-9]+)", "政\\s*治[：:：]\\s*([0-9]+)", "政治[：:：]\\s*([0-9]+)", "思政[：:：]\\s*([0-9]+)"], "geography": ["地\\s*理[：:：]\\s*([0-9]+)", "地理[：:：]\\s*([0-9]+)"]}, "validation_rules": {"id_number_length": [15, 18], "exam_number_length": [8, 15], "score_range": {"min": 0, "max": 150}, "total_score_range": {"min": 0, "max": 900}, "required_fields": ["name", "total_score"], "score_fields": ["chinese", "math", "foreign_language", "history", "politics", "geography"]}, "output_settings": {"excel_format": "xlsx", "include_ocr_text": true, "include_summary": true, "auto_open_result": false}, "processing_settings": {"max_workers": 4, "batch_size": 10, "retry_count": 2, "timeout_seconds": 30}}