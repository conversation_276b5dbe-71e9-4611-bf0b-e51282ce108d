"""
数据提取和验证模块
从OCR识别的文字中提取结构化数据
"""

import re
import json
from utils import ConfigManager, DataValidator, Logger


class DataExtractor:
    """数据提取器类"""

    def __init__(self, config_manager=None, logger=None):
        """
        初始化数据提取器

        Args:
            config_manager (ConfigManager): 配置管理器
            logger (Logger): 日志记录器
        """
        self.config = config_manager or ConfigManager()
        self.logger = logger or Logger()
        self.validator = DataValidator(self.config)

        # 从配置文件加载正则表达式模式
        self.patterns = self.config.get("extraction_patterns", self._get_default_patterns())

    def _get_default_patterns(self):
        """
        获取默认的正则表达式模式

        Returns:
            dict: 默认模式字典
        """
        return {
            'name': [
                r'姓\s*名[：:：]\s*([^\s\n]+)',
                r'姓名\s*[：:：]\s*([^\s\n]+)',
                r'考生姓名[：:：]\s*([^\s\n]+)'
            ],
            'gender': [
                r'性\s*别[：:：]\s*([男女])',
                r'性别\s*[：:：]\s*([男女])'
            ],
            'id_number': [
                r'身份证号[：:：]\s*([0-9X]{15,18})',
                r'身份证号码[：:：]\s*([0-9X]{15,18})',
                r'证件号码[：:：]\s*([0-9X]{15,18})'
            ],
            'exam_number': [
                r'考生号[：:：]\s*([0-9]{8,15})',
                r'准考证号[：:：]\s*([0-9]{8,15})',
                r'考号[：:：]\s*([0-9]{8,15})'
            ],
            'certificate_number': [
                r'证书编号[：:：]\s*([A-Z0-9\-]+)',
                r'证书号[：:：]\s*([A-Z0-9\-]+)',
                r'编号[：:：]\s*([A-Z0-9\-]+)'
            ],
            'total_score': [
                r'总\s*分[：:：]\s*([0-9]+)',
                r'总分[：:：]\s*([0-9]+)',
                r'合计[：:：]\s*([0-9]+)'
            ],
            'chinese': [
                r'语\s*文[：:：]\s*([0-9]+)',
                r'语文[：:：]\s*([0-9]+)'
            ],
            'math': [
                r'数\s*学[：:：]\s*([0-9]+)',
                r'数学[：:：]\s*([0-9]+)'
            ],
            'foreign_language': [
                r'外\s*语[：:：]\s*([0-9]+)',
                r'外语[：:：]\s*([0-9]+)',
                r'英\s*语[：:：]\s*([0-9]+)',
                r'英语[：:：]\s*([0-9]+)'
            ],
            'history': [
                r'历\s*史[：:：]\s*([0-9]+)',
                r'历史[：:：]\s*([0-9]+)'
            ],
            'politics': [
                r'思想政治[：:：]\s*([0-9]+)',
                r'政\s*治[：:：]\s*([0-9]+)',
                r'政治[：:：]\s*([0-9]+)'
            ],
            'geography': [
                r'地\s*理[：:：]\s*([0-9]+)',
                r'地理[：:：]\s*([0-9]+)'
            ]
        }
    
    def extract_field(self, text, field_name):
        """
        从文本中提取指定字段

        Args:
            text (str): 要搜索的文本
            field_name (str): 字段名称

        Returns:
            str: 提取到的值，如果未找到返回None
        """
        if field_name not in self.patterns:
            self.logger.warning(f"未找到字段 '{field_name}' 的提取模式")
            return None

        patterns = self.patterns[field_name]

        for i, pattern in enumerate(patterns):
            try:
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    extracted_value = match.group(1).strip()
                    self.logger.info(f"字段 '{field_name}' 使用模式 {i+1} 提取成功: {extracted_value}")
                    return extracted_value
            except re.error as e:
                self.logger.error(f"正则表达式错误 (字段: {field_name}, 模式: {pattern}): {str(e)}")
                continue

        self.logger.warning(f"未能提取字段 '{field_name}'")
        return None

    def extract_field_with_context(self, text, field_name, context_lines=2):
        """
        从文本中提取指定字段，并返回上下文信息

        Args:
            text (str): 要搜索的文本
            field_name (str): 字段名称
            context_lines (int): 上下文行数

        Returns:
            dict: 包含值和上下文的字典
        """
        if field_name not in self.patterns:
            return {"value": None, "context": None}

        patterns = self.patterns[field_name]
        text_lines = text.split('\n')

        for pattern in patterns:
            try:
                for i, line in enumerate(text_lines):
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        # 获取上下文
                        start_line = max(0, i - context_lines)
                        end_line = min(len(text_lines), i + context_lines + 1)
                        context = '\n'.join(text_lines[start_line:end_line])

                        return {
                            "value": match.group(1).strip(),
                            "context": context,
                            "line_number": i + 1
                        }
            except re.error:
                continue

        return {"value": None, "context": None}
    
    def extract_all_data(self, text):
        """
        从文本中提取所有数据

        Args:
            text (str): OCR识别的文本

        Returns:
            dict: 包含所有提取数据的字典
        """
        data = {}

        # 先尝试传统的正则表达式提取
        data['姓名'] = self.extract_field(text, 'name')
        data['性别'] = self.extract_field(text, 'gender')
        data['身份证号'] = self.extract_field(text, 'id_number')
        data['考生号'] = self.extract_field(text, 'exam_number')
        data['证书编号'] = self.extract_field(text, 'certificate_number')
        data['总分'] = self.extract_field(text, 'total_score')
        data['语文'] = self.extract_field(text, 'chinese')
        data['数学'] = self.extract_field(text, 'math')
        data['外语'] = self.extract_field(text, 'foreign_language')
        data['历史'] = self.extract_field(text, 'history')
        data['思想政治'] = self.extract_field(text, 'politics')
        data['地理'] = self.extract_field(text, 'geography')

        # 如果传统方法失败，尝试智能提取（针对EasyOCR分离的文字）
        if not any(data.values()):
            data = self.smart_extract_data(text)

        # 总是尝试使用增强的分数提取算法来改进成绩识别
        enhanced_data = self.enhanced_score_extraction(text)

        # 智能合并结果：增强算法的分数结果优先
        for key, value in enhanced_data.items():
            if value and value != '未识别':
                # 对于分数相关字段，增强算法的结果优先
                if key in ['语文', '数学', '外语', '历史', '思想政治', '地理', '总分']:
                    data[key] = value
                # 对于其他字段，如果原数据没有，则使用增强结果
                elif not data.get(key) or data.get(key) == '未识别' or data.get(key) == '':
                    data[key] = value

        return data

    def smart_extract_data(self, text):
        """
        智能提取数据（针对EasyOCR分离识别的情况）

        Args:
            text (str): OCR识别的文本

        Returns:
            dict: 包含提取数据的字典
        """
        data = {}
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # 先识别所有的数字和特殊格式
        numbers = []
        id_numbers = []
        exam_numbers = []
        certificate_numbers = []
        chinese_names = []

        for line in lines:
            # 身份证号 (15或18位数字，也接受17位的情况)
            if re.match(r'^\d{15}$|^\d{17,18}$', line):
                id_numbers.append(line)
            # 考生号 (8-15位数字)
            elif re.match(r'^\d{8,15}$', line) and line not in id_numbers:
                exam_numbers.append(line)
            # 证书编号 (字母数字组合)
            elif re.match(r'^[A-Z]\d{10,15}$', line):
                certificate_numbers.append(line)
            # 分数 (1-3位数字，且小于等于750)
            elif re.match(r'^\d{1,3}$', line) and int(line) <= 750:
                numbers.append(int(line))
            # 中文姓名（排除科目名称和其他关键词）
            elif re.match(r'^[\u4e00-\u9fff]{2,4}$', line):
                # 排除科目名称和常见关键词
                excluded_words = ['总分', '语文', '数学', '外语', '英语', '历史', '政治', '思想政治', '地理',
                                '性别', '姓名', '考生', '证书', '编号', '分数', '成绩', '合计']
                if line not in excluded_words:
                    chinese_names.append(line)

        # 分配识别到的数据
        if id_numbers:
            data['身份证号'] = id_numbers[0]

        if exam_numbers:
            data['考生号'] = exam_numbers[0]

        if certificate_numbers:
            data['证书编号'] = certificate_numbers[0]

        if chinese_names:
            # 智能选择最可能的姓名
            best_name = self.select_best_name(chinese_names, lines)
            if best_name:
                data['姓名'] = best_name

        # 智能分配分数
        if numbers:
            # 按分数大小排序
            numbers.sort(reverse=True)

            # 查找总分（通常是最大的分数）
            total_score_candidates = [n for n in numbers if n > 200]  # 总分通常大于200
            if total_score_candidates:
                data['总分'] = str(total_score_candidates[0])
                numbers.remove(total_score_candidates[0])
            elif numbers:  # 如果没有大于200的分数，取最大的
                data['总分'] = str(numbers[0])
                numbers.remove(numbers[0])

            # 分配各科成绩（根据常见分数范围）
            subject_scores = [n for n in numbers if 0 <= n <= 150]  # 单科分数通常0-150

            # 根据文本中的科目标签来智能分配
            subject_mapping = self.map_scores_to_subjects(lines, subject_scores)
            data.update(subject_mapping)

            # 如果没有找到科目标签，尝试按顺序分配常见科目
            if not subject_mapping and subject_scores:
                self.assign_scores_by_order(data, subject_scores)

        # 查找性别
        for line in lines:
            if '男' in line or '女' in line:
                if '男' in line:
                    data['性别'] = '男'
                elif '女' in line:
                    data['性别'] = '女'
                break

        return data

    def map_scores_to_subjects(self, lines, scores):
        """
        根据文本中的科目标签智能分配分数

        Args:
            lines (list): 文本行列表
            scores (list): 分数列表

        Returns:
            dict: 科目分数映射
        """
        subject_mapping = {}

        # 科目关键词
        subjects = {
            '语文': ['语文', 'Chinese'],
            '数学': ['数学', 'Math'],
            '外语': ['外语', 'English', '英语'],
            '历史': ['历史', 'History'],
            '思想政治': ['思想政治', '政治', 'Politics'],
            '地理': ['地理', 'Geography']
        }

        # 查找每个科目在文本中的位置
        subject_positions = {}
        for i, line in enumerate(lines):
            for subject_name, keywords in subjects.items():
                for keyword in keywords:
                    if keyword in line:
                        subject_positions[subject_name] = i
                        break

        # 查找分数在文本中的位置
        score_positions = {}
        for i, line in enumerate(lines):
            if line.isdigit() and int(line) in scores:
                score_positions[i] = int(line)

        # 根据位置关系分配分数（就近原则）
        for subject_name, subject_pos in subject_positions.items():
            closest_score = None
            min_distance = float('inf')

            for score_pos, score in score_positions.items():
                distance = abs(score_pos - subject_pos)
                if distance < min_distance and score not in subject_mapping.values():
                    min_distance = distance
                    closest_score = score

            if closest_score is not None:
                subject_mapping[subject_name] = str(closest_score)

        return subject_mapping

    def assign_scores_by_order(self, data, scores):
        """
        按顺序分配分数到常见科目

        Args:
            data (dict): 数据字典
            scores (list): 分数列表
        """
        # 常见科目顺序（根据高考成绩单的常见排列）
        common_subjects = ['语文', '数学', '外语', '历史', '思想政治', '地理']

        # 按分数从高到低排序
        scores.sort(reverse=True)

        # 分配分数到科目
        for i, subject in enumerate(common_subjects):
            if i < len(scores) and not data.get(subject):
                data[subject] = str(scores[i])

    def enhanced_score_extraction(self, text):
        """
        增强的分数提取方法

        Args:
            text (str): OCR识别的文本

        Returns:
            dict: 提取的数据
        """
        data = {}
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # 查找所有数字及其在文本中的位置
        import re

        # 创建一个包含行号和内容的列表
        line_info = []
        for i, line in enumerate(lines):
            line_info.append((i, line))

        # 查找科目关键词的位置
        subject_positions = {}
        subjects = {
            '语文': ['语文', 'Chinese', '语', 'Lang'],
            '数学': ['数学', 'Math', '数', 'Mathematics'],
            '外语': ['外语', 'English', '英语', '英', 'Foreign', 'Eng'],
            '历史': ['历史', 'History', '史', 'Hist'],
            '思想政治': ['思想政治', '政治', 'Politics', '政', '思政', 'Pol'],
            '地理': ['地理', 'Geography', '地', 'Geo'],
            '总分': ['总分', 'Total', '合计', '总', 'Sum', 'Score']
        }

        for subject_name, keywords in subjects.items():
            for i, line in enumerate(lines):
                for keyword in keywords:
                    if keyword in line:
                        subject_positions[subject_name] = i
                        break
                if subject_name in subject_positions:
                    break

        # 查找所有可能的分数
        score_positions = {}
        for i, line in enumerate(lines):
            # 查找纯数字行
            if re.match(r'^\d{1,3}$', line):
                score = int(line)
                if 0 <= score <= 750:  # 合理的分数范围
                    score_positions[i] = score

        # 根据位置关系分配分数 - 改进版本
        used_scores = set()

        # 首先处理总分（优先级最高）
        if '总分' in subject_positions:
            total_pos = subject_positions['总分']
            best_total_score = None
            best_total_pos = None

            # 查找总分附近最大的分数
            for score_pos, score in score_positions.items():
                distance = abs(score_pos - total_pos)
                if distance <= 3 and score > 200:  # 总分通常大于200且距离较近
                    if best_total_score is None or score > best_total_score:
                        best_total_score = score
                        best_total_pos = score_pos

            if best_total_score:
                data['总分'] = str(best_total_score)
                used_scores.add(best_total_pos)

        # 然后处理各科成绩（按在文本中的出现顺序）
        subject_order = ['语文', '数学', '外语', '历史', '思想政治', '地理']
        available_subjects = [s for s in subject_order if s in subject_positions]

        for subject_name in available_subjects:
            if data.get(subject_name):  # 如果已经有值，跳过
                continue

            subject_pos = subject_positions[subject_name]
            best_score = None
            best_pos = None
            min_weighted_distance = float('inf')

            # 查找最适合的分数
            for score_pos, score in score_positions.items():
                if score_pos in used_scores or score > 150:  # 跳过已使用的和过大的分数
                    continue

                distance = abs(score_pos - subject_pos)

                # 计算加权距离
                if score_pos > subject_pos:
                    # 科目后面的分数，优先考虑
                    if score_pos == subject_pos + 1:
                        weighted_distance = distance * 0.1  # 紧邻下一行，权重最高
                    else:
                        weighted_distance = distance * 0.5  # 后面的行，权重较高
                else:
                    # 科目前面的分数，权重较低
                    weighted_distance = distance * 2.0

                if weighted_distance < min_weighted_distance:
                    min_weighted_distance = weighted_distance
                    best_score = score
                    best_pos = score_pos

            # 分配分数（距离不能太远）
            if best_score is not None and min_weighted_distance <= 3:
                data[subject_name] = str(best_score)
                used_scores.add(best_pos)

        # 如果还有未分配的分数，尝试按顺序分配给缺失的科目
        remaining_scores = [(pos, score) for pos, score in score_positions.items()
                          if pos not in used_scores and score <= 150]
        remaining_scores.sort(key=lambda x: x[1], reverse=True)  # 按分数从高到低排序

        missing_subjects = [s for s in subject_order if not data.get(s)]

        for i, subject in enumerate(missing_subjects):
            if i < len(remaining_scores):
                data[subject] = str(remaining_scores[i][1])

        return data

    def extract_value_from_line(self, line, keyword):
        """
        从包含关键词的行中提取值

        Args:
            line (str): 文本行
            keyword (str): 关键词

        Returns:
            str: 提取的值
        """
        # 移除关键词和常见的分隔符
        value = line.replace(keyword, '').strip()

        # 移除常见的分隔符和符号
        for sep in [':', '：', '(', ')', '[', ']', '（', '）', '【', '】']:
            value = value.replace(sep, '').strip()

        # 如果剩余的是有效值，返回
        if value and len(value) > 0:
            # 检查是否是纯数字（分数）或身份证号
            if value.isdigit() or self.is_valid_id_number(value) or self.is_chinese_name(value):
                return value

        return None

    def extract_value_from_next_line(self, line):
        """
        从下一行提取值

        Args:
            line (str): 文本行

        Returns:
            str: 提取的值
        """
        line = line.strip()

        # 检查是否是有效的值
        if line and len(line) > 0:
            # 身份证号格式
            if re.match(r'^\d{15}|\d{18}$', line):
                return line
            # 考生号格式
            if re.match(r'^\d{8,15}$', line):
                return line
            # 证书编号格式
            if re.match(r'^[A-Z0-9]{8,20}$', line):
                return line
            # 分数格式
            if re.match(r'^\d{1,3}$', line) and int(line) <= 750:
                return line
            # 中文姓名
            if self.is_chinese_name(line):
                return line
            # 性别
            if line in ['男', '女']:
                return line

        return None

    def is_valid_id_number(self, text):
        """检查是否是有效的身份证号"""
        return bool(re.match(r'^\d{15}|\d{18}$', text))

    def is_chinese_name(self, text):
        """检查是否是中文姓名"""
        return bool(re.match(r'^[\u4e00-\u9fff]{2,4}$', text))

    def select_best_name(self, chinese_names, lines):
        """
        从候选姓名中选择最可能的真实姓名

        Args:
            chinese_names (list): 候选姓名列表
            lines (list): 所有文本行

        Returns:
            str: 最可能的姓名
        """
        if not chinese_names:
            return None

        # 如果只有一个候选，直接返回
        if len(chinese_names) == 1:
            return chinese_names[0]

        # 评分系统：根据位置和上下文选择最佳姓名
        name_scores = {}

        for name in chinese_names:
            score = 0

            # 查找姓名在文本中的位置
            for i, line in enumerate(lines):
                if name in line:
                    # 位置评分：越靠前的越可能是姓名
                    position_score = max(0, 10 - i)
                    score += position_score

                    # 上下文评分：检查前后行是否有相关关键词
                    context_lines = []
                    if i > 0:
                        context_lines.append(lines[i-1])
                    if i < len(lines) - 1:
                        context_lines.append(lines[i+1])

                    context_text = ' '.join(context_lines)

                    # 如果上下文包含个人信息相关词汇，加分
                    if any(keyword in context_text for keyword in ['性别', '身份证', '考生号']):
                        score += 5

                    # 如果上下文包含成绩相关词汇，减分
                    if any(keyword in context_text for keyword in ['总分', '语文', '数学', '外语']):
                        score -= 3

                    break

            # 常见姓氏加分
            common_surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
                             '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
            if name and name[0] in common_surnames:
                score += 3

            name_scores[name] = score

        # 返回得分最高的姓名
        best_name = max(name_scores.items(), key=lambda x: x[1])
        return best_name[0] if best_name[1] > 0 else None
    
    def validate_scores(self, data):
        """
        验证分数的合理性（适应简化版和完整版成绩单）

        Args:
            data (dict): 包含成绩数据的字典

        Returns:
            dict: 包含验证结果的字典
        """
        try:
            # 获取各科成绩
            subjects = ['语文', '数学', '外语', '历史', '思想政治', '地理']
            subject_scores = []
            has_subject_scores = False

            for subject in subjects:
                score_str = data.get(subject)
                if score_str and score_str != '未识别' and score_str != '' and score_str.isdigit():
                    subject_scores.append(int(score_str))
                    has_subject_scores = True
                else:
                    subject_scores.append(0)

            # 计算各科成绩总和
            calculated_total = sum(subject_scores)

            # 获取识别的总分
            total_score_str = data.get('总分')
            recognized_total = int(total_score_str) if total_score_str and total_score_str.isdigit() else 0

            # 验证逻辑
            if not has_subject_scores:
                # 简化版成绩单：只有总分，没有各科成绩
                # 只验证总分的合理性
                is_valid = 0 <= recognized_total <= 750 and recognized_total > 0
            else:
                # 完整版成绩单：验证总分是否等于各科之和
                is_valid = calculated_total == recognized_total

            return {
                'is_valid': is_valid,
                'calculated_total': calculated_total,
                'recognized_total': recognized_total,
                'subject_scores': dict(zip(subjects, subject_scores)),
                'difference': abs(calculated_total - recognized_total),
                'has_subject_scores': has_subject_scores
            }

        except Exception as e:
            return {
                'is_valid': False,
                'error': str(e),
                'calculated_total': 0,
                'recognized_total': 0,
                'has_subject_scores': False
            }
    
    def clean_data(self, data):
        """
        清理和标准化数据
        
        Args:
            data (dict): 原始数据
            
        Returns:
            dict: 清理后的数据
        """
        cleaned_data = {}
        
        for key, value in data.items():
            if value is None:
                cleaned_data[key] = ''
            else:
                # 去除多余的空格和特殊字符
                cleaned_value = str(value).strip()
                cleaned_data[key] = cleaned_value
        
        return cleaned_data
    
    def process_image_data(self, text, image_path):
        """
        处理单张图片的数据提取
        
        Args:
            text (str): OCR识别的文本
            image_path (str): 图片路径
            
        Returns:
            dict: 处理结果
        """
        # 提取数据
        raw_data = self.extract_all_data(text)
        
        # 清理数据
        cleaned_data = self.clean_data(raw_data)
        
        # 验证成绩
        validation_result = self.validate_scores(cleaned_data)
        
        # 添加图片路径和验证信息
        result = {
            'image_path': image_path,
            'data': cleaned_data,
            'validation': validation_result
        }
        
        return result


def test_extractor():
    """测试数据提取功能"""
    extractor = DataExtractor()
    
    # 测试文本
    test_text = """
    高考成绩通知单
    姓名：张三
    性别：男
    身份证号：123456789012345678
    考生号：1234567890
    证书编号：ABC123456
    
    成绩：
    语文：120
    数学：130
    外语：125
    历史：85
    思想政治：90
    地理：88
    总分：638
    """
    
    result = extractor.process_image_data(test_text, "test.png")
    print("提取结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    test_extractor()
