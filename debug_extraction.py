"""
调试数据提取过程
"""

from utils import ConfigManager, Logger
from data_extractor import DataExtractor


def debug_extraction_process():
    """调试提取过程"""
    config = ConfigManager()
    logger = Logger(enable_console=False)
    extractor = DataExtractor(config, logger)
    
    # 测试用例2
    test_text = """全国普通高等学校招生统一考试
500383200511151178
24500118183006
总分
301
外语
120
语文
90
数学
91"""
    
    print("调试文本:")
    print("="*40)
    print(test_text)
    print("="*40)
    
    lines = [line.strip() for line in test_text.split('\n') if line.strip()]
    print(f"\n处理后的行数: {len(lines)}")
    for i, line in enumerate(lines):
        print(f"{i:2d}: {line}")
    
    # 手动执行提取过程
    print("\n科目识别:")
    print("-"*30)
    
    subjects = {
        '语文': ['语文', 'Chinese', '语', 'Lang'],
        '数学': ['数学', 'Math', '数', 'Mathematics'],
        '外语': ['外语', 'English', '英语', '英', 'Foreign', 'Eng'],
        '历史': ['历史', 'History', '史', 'Hist'],
        '思想政治': ['思想政治', '政治', 'Politics', '政', '思政', 'Pol'],
        '地理': ['地理', 'Geography', '地', 'Geo'],
        '总分': ['总分', 'Total', '合计', '总', 'Sum', 'Score']
    }
    
    subject_positions = {}
    for subject_name, keywords in subjects.items():
        for i, line in enumerate(lines):
            for keyword in keywords:
                if keyword in line:
                    subject_positions[subject_name] = i
                    print(f"  {subject_name}: 第{i}行 '{line}' (关键词: {keyword})")
                    break
            if subject_name in subject_positions:
                break
    
    print("\n分数识别:")
    print("-"*30)
    
    import re
    score_positions = {}
    for i, line in enumerate(lines):
        if re.match(r'^\d{1,3}$', line):
            score = int(line)
            if 0 <= score <= 750:
                score_positions[i] = score
                print(f"  第{i}行: {score}")
    
    print("\n位置关系分析:")
    print("-"*30)
    
    for subject_name, subject_pos in subject_positions.items():
        print(f"\n{subject_name} (第{subject_pos}行):")
        for score_pos, score in score_positions.items():
            distance = abs(score_pos - subject_pos)
            direction = "后面" if score_pos > subject_pos else "前面" if score_pos < subject_pos else "同行"
            print(f"  分数{score} (第{score_pos}行): 距离{distance}, 在{direction}")
    
    # 执行实际提取
    print("\n实际提取结果:")
    print("-"*30)
    result = extractor.enhanced_score_extraction(test_text)
    for key, value in result.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    debug_extraction_process()
