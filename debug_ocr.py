"""
调试OCR返回格式
"""

import os
from paddleocr import PaddleOCR
import json


def debug_paddleocr():
    """调试PaddleOCR的返回格式"""
    print("初始化PaddleOCR...")
    
    try:
        ocr = PaddleOCR(lang='ch')
        print("✅ OCR初始化成功")
    except Exception as e:
        print(f"❌ OCR初始化失败: {str(e)}")
        return
    
    # 查找第一张图片
    imgs_dir = "imgs"
    if not os.path.exists(imgs_dir):
        print("❌ imgs文件夹不存在")
        return
    
    image_files = [f for f in os.listdir(imgs_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    if not image_files:
        print("❌ 没有找到图片文件")
        return
    
    test_image = os.path.join(imgs_dir, image_files[0])
    print(f"测试图片: {test_image}")
    
    try:
        # 测试OCR识别
        print("开始OCR识别...")
        result = ocr.ocr(test_image)
        
        print("=" * 50)
        print("OCR返回结果类型:", type(result))
        print("OCR返回结果长度:", len(result) if hasattr(result, '__len__') else "无长度")
        print("=" * 50)
        
        # 打印详细结果结构
        if result:
            print("result[0] 类型:", type(result[0]) if len(result) > 0 else "无")
            if len(result) > 0 and result[0]:
                ocr_result = result[0]
                print("OCRResult 属性:", dir(ocr_result))

                # 尝试访问OCRResult的属性
                if hasattr(ocr_result, 'text'):
                    print("OCRResult.text:", ocr_result.text)
                if hasattr(ocr_result, 'boxes'):
                    print("OCRResult.boxes:", ocr_result.boxes)
                if hasattr(ocr_result, 'scores'):
                    print("OCRResult.scores:", ocr_result.scores)
                if hasattr(ocr_result, 'texts'):
                    print("OCRResult.texts:", ocr_result.texts)

                # 尝试迭代
                try:
                    for i, item in enumerate(ocr_result):
                        print(f"OCRResult[{i}] 类型:", type(item))
                        print(f"OCRResult[{i}] 内容:", item)
                        if i >= 2:  # 只显示前3个
                            break
                except Exception as e:
                    print(f"无法迭代OCRResult: {str(e)}")
        
        # 保存完整结果到文件
        with open("debug_ocr_result.json", "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        print("完整结果已保存到 debug_ocr_result.json")
        
        # 尝试提取文字
        print("\n尝试提取文字...")
        extracted_texts = []
        
        if result and isinstance(result, list):
            for page_result in result:
                if page_result and isinstance(page_result, list):
                    for line in page_result:
                        try:
                            if isinstance(line, dict):
                                # 字典格式
                                if 'text' in line:
                                    extracted_texts.append(line['text'])
                                    print(f"提取文字 (字典): {line['text']}")
                            elif isinstance(line, (list, tuple)) and len(line) >= 2:
                                # 列表格式
                                if isinstance(line[1], (list, tuple)) and len(line[1]) >= 1:
                                    extracted_texts.append(line[1][0])
                                    print(f"提取文字 (列表): {line[1][0]}")
                                elif isinstance(line[1], str):
                                    extracted_texts.append(line[1])
                                    print(f"提取文字 (字符串): {line[1]}")
                        except Exception as e:
                            print(f"提取文字失败: {str(e)}")
                            print(f"line 类型: {type(line)}")
                            print(f"line 内容: {line}")
        
        print(f"\n总共提取到 {len(extracted_texts)} 个文字")
        
    except Exception as e:
        print(f"❌ OCR识别失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_paddleocr()
