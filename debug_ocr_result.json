[{"input_path": "imgs\\file0010_00.png", "page_index": null, "doc_preprocessor_res": {"input_path": null, "page_index": null, "input_img": "[[[236 ... 229]\n  ...\n  [202 ... 194]]\n\n ...\n\n [[255 ... 255]\n  ...\n  [255 ... 255]]]", "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0, "rot_img": "[[[236 ... 229]\n  ...\n  [202 ... 194]]\n\n ...\n\n [[255 ... 255]\n  ...\n  [255 ... 255]]]", "output_img": "[[[142 ...  84]\n  ...\n  [241 ... 185]]\n\n ...\n\n [[155 ...  60]\n  ...\n  [133 ...  42]]]"}, "dt_polys": ["[[222  76]\n ...\n [222 100]]", "[[216 102]\n ...\n [216 116]]", "[[293 125]\n ...\n [293 161]]", "[[294 161]\n ...\n [294 178]]", "[[ 72 185]\n ...\n [ 71 203]]", "[[237 189]\n ...\n [237 206]]", "[[ 69 214]\n ...\n [ 69 232]]", "[[ 68 244]\n ...\n [ 68 261]]", "[[ 67 273]\n ...\n [ 67 290]]", "[[ 66 298]\n ...\n [ 66 315]]", "[[ 92 316]\n ...\n [ 92 330]]", "[[ 64 329]\n ...\n [ 64 347]]", "[[ 85 355]\n ...\n [ 85 378]]", "[[158 356]\n ...\n [158 379]]", "[[229 356]\n ...\n [229 379]]", "[[303 358]\n ...\n [303 378]]", "[[373 357]\n ...\n [373 380]]", "[[ 78 379]\n ...\n [ 77 394]]", "[[160 382]\n ...\n [160 397]]", "[[235 382]\n ...\n [235 397]]", "[[302 376]\n ...\n [300 391]]", "[[298 389]\n ...\n [297 403]]", "[[375 384]\n ...\n [375 396]]", "[[442 389]\n ...\n [441 403]]", "[[444 380]\n ...\n [444 391]]", "[[512 386]\n ...\n [512 394]]", "[[533 381]\n ...\n [533 392]]", "[[ 89 400]\n ...\n [ 89 419]]", "[[167 401]\n ...\n [167 418]]", "[[239 401]\n ...\n [239 419]]", "[[310 399]\n ...\n [308 416]]", "[[453 403]\n ...\n [453 414]]", "[[532 405]\n ...\n [532 434]]", "[[367 445]\n ...\n [367 462]]", "[[441 446]\n ...\n [441 460]]", "[[438 459]\n ...\n [437 475]]", "[[339 474]\n ...\n [339 494]]"], "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "text_rec_score_thresh": 0.0, "rec_texts": ["全国普通高等学校招生统一考试", "TheUnifiedExaminationsforAdmissions of National Universities&Colleges", "成绩证书", "TranscriptsCertificate", "姓名（Name）：贺德术", "性别（Sex）：男（Male）", "身份证号（IDCard）：500229200605285714", "考生号（EXAM.No.）：24500118141142", "证书编号（CertificateNo.）：PZ245014012896", "该生参加2024年全国普通高等学校招生统一考试，各科成绩如下：（满分：750）", "Thisstudent hastaken partinYear2024Unified Examinationfor Admissionsof NationalUniverities", "andCollegeswitheachacademicrecordasfollows:（PerfectScore:750)", "总分", "语文", "数学", "外语", "历史", "Total Score", "Chinese", "Maths", "Foreign", "Language", "Hist", "Ideologs", "oliticsand", "G", "", "271", "68", "21", "49", "38", "H", "发证", "庆市教育考试", "Authority:", "Chongqing edution examinations autho"], "rec_scores": [0.9967500567436218, 0.9589406847953796, 0.9989339113235474, 0.990420401096344, 0.9712302088737488, 0.9465028643608093, 0.9666066765785217, 0.9613556265830994, 0.980069637298584, 0.9817234873771667, 0.8850500583648682, 0.9238803386688232, 0.9989964962005615, 0.994766891002655, 0.9989968538284302, 0.9973618388175964, 0.9990876913070679, 0.9528946280479431, 0.9831182360649109, 0.9807920455932617, 0.913271963596344, 0.9877451062202454, 0.9108765125274658, 0.8306322693824768, 0.93849116563797, 0.7337105870246887, 0.0, 0.9989445209503174, 0.9985724687576294, 0.998928964138031, 0.9990206956863403, 0.9987145066261292, 0.0846429094672203, 0.9984954595565796, 0.9800148606300354, 0.9828161001205444, 0.8076088428497314], "rec_polys": ["[[222  76]\n ...\n [222 100]]", "[[216 102]\n ...\n [216 116]]", "[[293 125]\n ...\n [293 161]]", "[[294 161]\n ...\n [294 178]]", "[[ 72 185]\n ...\n [ 71 203]]", "[[237 189]\n ...\n [237 206]]", "[[ 69 214]\n ...\n [ 69 232]]", "[[ 68 244]\n ...\n [ 68 261]]", "[[ 67 273]\n ...\n [ 67 290]]", "[[ 66 298]\n ...\n [ 66 315]]", "[[ 92 316]\n ...\n [ 92 330]]", "[[ 64 329]\n ...\n [ 64 347]]", "[[ 85 355]\n ...\n [ 85 378]]", "[[158 356]\n ...\n [158 379]]", "[[229 356]\n ...\n [229 379]]", "[[303 358]\n ...\n [303 378]]", "[[373 357]\n ...\n [373 380]]", "[[ 78 379]\n ...\n [ 77 394]]", "[[160 382]\n ...\n [160 397]]", "[[235 382]\n ...\n [235 397]]", "[[302 376]\n ...\n [300 391]]", "[[298 389]\n ...\n [297 403]]", "[[375 384]\n ...\n [375 396]]", "[[442 389]\n ...\n [441 403]]", "[[444 380]\n ...\n [444 391]]", "[[512 386]\n ...\n [512 394]]", "[[533 381]\n ...\n [533 392]]", "[[ 89 400]\n ...\n [ 89 419]]", "[[167 401]\n ...\n [167 418]]", "[[239 401]\n ...\n [239 419]]", "[[310 399]\n ...\n [308 416]]", "[[453 403]\n ...\n [453 414]]", "[[532 405]\n ...\n [532 434]]", "[[367 445]\n ...\n [367 462]]", "[[441 446]\n ...\n [441 460]]", "[[438 459]\n ...\n [437 475]]", "[[339 474]\n ...\n [339 494]]"], "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "rec_boxes": "[[222 ... 100]\n ...\n [339 ... 495]]"}]