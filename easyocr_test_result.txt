EasyOCR识别结果
==================================================
图片: imgs\file0010_00.png
识别到 43 个文字区域

 1. 全国普通高等学校招生统一考试 (置信度: 0.778)
    边界框: [[np.int32(228), np.int32(87)], [np.int32(577), np.int32(87)], [np.int32(577), np.int32(115)], [np.int32(228), np.int32(115)]]

 2. <PERSON><PERSON> Uiled Examnallons or Admisslons ol Nalional Uniersities (置信度: 0.193)
    边界框: [[np.int32(223), np.int32(113)], [np.int32(529), np.int32(113)], [np.int32(529), np.int32(127)], [np.int32(223), np.int32(127)]]

 3. Colleges (置信度: 0.544)
    边界框: [[np.int32(537), np.int32(113)], [np.int32(583), np.int32(113)], [np.int32(583), np.int32(127)], [np.int32(537), np.int32(127)]]

 4. 戌  绩  证 势 (置信度: 0.006)
    边界框: [[np.int32(295), np.int32(127)], [np.int32(507), np.int32(127)], [np.int32(507), np.int32(169)], [np.int32(295), np.int32(169)]]

 5. Transcripts Certificate (置信度: 0.959)
    边界框: [[np.int32(298), np.int32(164)], [np.int32(505), np.int32(164)], [np.int32(505), np.int32(183)], [np.int32(298), np.int32(183)]]

 6. 姓名(Name; (置信度: 0.149)
    边界框: [[np.int32(101), np.int32(189)], [np.int32(179), np.int32(189)], [np.int32(179), np.int32(207)], [np.int32(101), np.int32(207)]]

 7. 贺德术 (置信度: 0.744)
    边界框: [[np.int32(185), np.int32(191)], [np.int32(229), np.int32(191)], [np.int32(229), np.int32(207)], [np.int32(185), np.int32(207)]]

 8. 性别[Sey): (置信度: 0.103)
    边界框: [[np.int32(249), np.int32(191)], [np.int32(311), np.int32(191)], [np.int32(311), np.int32(207)], [np.int32(249), np.int32(207)]]

 9. 男!Iale) (置信度: 0.174)
    边界框: [[np.int32(319), np.int32(191)], [np.int32(371), np.int32(191)], [np.int32(371), np.int32(207)], [np.int32(319), np.int32(207)]]

10. 身份证号([0 Cardj: (置信度: 0.118)
    边界框: [[np.int32(101), np.int32(213)], [np.int32(217), np.int32(213)], [np.int32(217), np.int32(233)], [np.int32(101), np.int32(233)]]

11. 500229200505285714 (置信度: 0.774)
    边界框: [[np.int32(223), np.int32(217)], [np.int32(337), np.int32(217)], [np.int32(337), np.int32(231)], [np.int32(223), np.int32(231)]]

12. 考生号(E《AJ.Vo): (置信度: 0.220)
    边界框: [[np.int32(101), np.int32(237)], [np.int32(219), np.int32(237)], [np.int32(219), np.int32(257)], [np.int32(101), np.int32(257)]]

13. 24500118141142 (置信度: 0.923)
    边界框: [[np.int32(227), np.int32(243)], [np.int32(317), np.int32(243)], [np.int32(317), np.int32(257)], [np.int32(227), np.int32(257)]]

14. 证书编号 (Ceriljcate Wo.): (置信度: 0.276)
    边界框: [[np.int32(101), np.int32(263)], [np.int32(249), np.int32(263)], [np.int32(249), np.int32(283)], [np.int32(101), np.int32(283)]]

15. P2245014012896 (置信度: 0.484)
    边界框: [[np.int32(255), np.int32(267)], [np.int32(347), np.int32(267)], [np.int32(347), np.int32(283)], [np.int32(255), np.int32(283)]]

16. 该生参加 2024年全国普通高等学校招生统 (置信度: 0.092)
    边界框: [[np.int32(103), np.int32(287)], [np.int32(347), np.int32(287)], [np.int32(347), np.int32(307)], [np.int32(103), np.int32(307)]]

17. 考试;各科成绩如下: (满分:750 ) (置信度: 0.629)
    边界框: [[np.int32(353), np.int32(289)], [np.int32(537), np.int32(289)], [np.int32(537), np.int32(307)], [np.int32(353), np.int32(307)]]

18. This sudenl his laken partin YeRr 2024 Unified Exaninaton Or (置信度: 0.205)
    边界框: [[np.int32(125), np.int32(305)], [np.int32(409), np.int32(305)], [np.int32(409), np.int32(319)], [np.int32(125), np.int32(319)]]

19. HIsn (置信度: 0.006)
    边界框: [[np.int32(412), np.int32(308)], [np.int32(452), np.int32(308)], [np.int32(452), np.int32(316)], [np.int32(412), np.int32(316)]]

20. Universities (置信度: 0.314)
    边界框: [[np.int32(515), np.int32(307)], [np.int32(571), np.int32(307)], [np.int32(571), np.int32(321)], [np.int32(515), np.int32(321)]]

21. M0 (置信度: 0.022)
    边界框: [[np.int32(101), np.int32(319)], [np.int32(123), np.int32(319)], [np.int32(123), np.int32(331)], [np.int32(101), np.int32(331)]]

22. wilh eachacadem;Crecord (置信度: 0.519)
    边界框: [[np.int32(161), np.int32(317)], [np.int32(279), np.int32(317)], [np.int32(279), np.int32(331)], [np.int32(161), np.int32(331)]]

23. Uou3 ( Pcrlecl Score: 750 (置信度: 0.110)
    边界框: [[np.int32(299), np.int32(319)], [np.int32(415), np.int32(319)], [np.int32(415), np.int32(333)], [np.int32(299), np.int32(333)]]

24. 总分 (置信度: 0.907)
    边界框: [[np.int32(117), np.int32(337)], [np.int32(151), np.int32(337)], [np.int32(151), np.int32(357)], [np.int32(117), np.int32(357)]]

25. 语文 (置信度: 0.619)
    边界框: [[np.int32(183), np.int32(339)], [np.int32(217), np.int32(339)], [np.int32(217), np.int32(359)], [np.int32(183), np.int32(359)]]

26. 数学 (置信度: 0.985)
    边界框: [[np.int32(247), np.int32(339)], [np.int32(281), np.int32(339)], [np.int32(281), np.int32(359)], [np.int32(247), np.int32(359)]]

27. 外语 (置信度: 0.649)
    边界框: [[np.int32(311), np.int32(339)], [np.int32(345), np.int32(339)], [np.int32(345), np.int32(359)], [np.int32(311), np.int32(359)]]

28. 猷: (置信度: 0.000)
    边界框: [[np.int32(436), np.int32(316)], [np.int32(492), np.int32(316)], [np.int32(492), np.int32(364)], [np.int32(436), np.int32(364)]]

29. 地理 (置信度: 0.990)
    边界框: [[np.int32(503), np.int32(339)], [np.int32(539), np.int32(339)], [np.int32(539), np.int32(359)], [np.int32(503), np.int32(359)]]

30. Tr (置信度: 0.130)
    边界框: [[np.int32(312), np.int32(360)], [np.int32(342), np.int32(360)], [np.int32(342), np.int32(368)], [np.int32(312), np.int32(368)]]

31. NTAIAC (置信度: 0.013)
    边界框: [[np.int32(440), np.int32(360)], [np.int32(474), np.int32(360)], [np.int32(474), np.int32(368)], [np.int32(440), np.int32(368)]]

32. TolSeop (置信度: 0.090)
    边界框: [[np.int32(113), np.int32(361)], [np.int32(157), np.int32(361)], [np.int32(157), np.int32(375)], [np.int32(113), np.int32(375)]]

33. ULCs (置信度: 0.097)
    边界框: [[np.int32(184), np.int32(364)], [np.int32(214), np.int32(364)], [np.int32(214), np.int32(372)], [np.int32(184), np.int32(372)]]

34. Vh (置信度: 0.716)
    边界框: [[np.int32(253), np.int32(363)], [np.int32(279), np.int32(363)], [np.int32(279), np.int32(375)], [np.int32(253), np.int32(375)]]

35. TIOMR (置信度: 0.006)
    边界框: [[np.int32(310), np.int32(372)], [np.int32(346), np.int32(372)], [np.int32(346), np.int32(378)], [np.int32(310), np.int32(378)]]

36. Jento (置信度: 0.288)
    边界框: [[np.int32(440), np.int32(372)], [np.int32(468), np.int32(372)], [np.int32(468), np.int32(380)], [np.int32(440), np.int32(380)]]

37. 271 (置信度: 0.998)
    边界框: [[np.int32(123), np.int32(379)], [np.int32(147), np.int32(379)], [np.int32(147), np.int32(395)], [np.int32(123), np.int32(395)]]

38. 尤#方 (置信度: 0.000)
    边界框: [[np.int32(439), np.int32(419)], [np.int32(483), np.int32(419)], [np.int32(483), np.int32(433)], [np.int32(439), np.int32(433)]]

39. Auhority: (置信度: 0.302)
    边界框: [[np.int32(432), np.int32(431)], [np.int32(481), np.int32(431)], [np.int32(481), np.int32(447)], [np.int32(432), np.int32(447)]]

40. Chongqing (置信度: 0.883)
    边界框: [[np.int32(348), np.int32(446)], [np.int32(397), np.int32(446)], [np.int32(397), np.int32(462)], [np.int32(348), np.int32(462)]]

41. CIOTTIRTTRS (置信度: 0.016)
    边界框: [[np.int32(440), np.int32(450)], [np.int32(494), np.int32(450)], [np.int32(494), np.int32(456)], [np.int32(440), np.int32(456)]]

42. IINI (置信度: 0.313)
    边界框: [[np.int32(498), np.int32(450)], [np.int32(522), np.int32(450)], [np.int32(522), np.int32(456)], [np.int32(498), np.int32(456)]]

43. Coleee (置信度: 0.115)
    边界框: [[np.float64(121.3902892391503), np.float64(317.2073760108954)], [np.float64(158.98710547774758), np.float64(320.83992884144635)], [np.float64(157.60971076084968), np.float64(331.7926239891046)], [np.float64(120.01289452225241), np.float64(327.16007115855365)]]

