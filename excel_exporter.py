"""
Excel导出模块
将提取的数据导出为Excel文件
"""

import pandas as pd
import os
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows


class ExcelExporter:
    """Excel导出器类"""
    
    def __init__(self):
        """初始化Excel导出器"""
        self.columns = [
            '图片文件名',
            '姓名',
            '性别', 
            '身份证号',
            '考生号',
            '证书编号',
            '总分',
            '语文',
            '数学',
            '外语',
            '历史',
            '思想政治',
            '地理',
            '计算总分',
            '分数验证',
            '差值'
        ]
    
    def prepare_data(self, results):
        """
        准备导出数据

        Args:
            results (list): 处理结果列表

        Returns:
            list: 准备好的数据行列表
        """
        data_rows = []

        for result in results:
            image_path = result.get('image_path', '')
            data = result.get('data', {})
            validation = result.get('validation', {})

            # 提取文件名
            filename = os.path.basename(image_path)

            # 确保身份证号和考生号以字符串形式存储，避免科学计数法
            id_number = data.get('身份证号', '')
            if id_number and id_number != '未识别':
                id_number = f"'{id_number}"  # 在前面加单引号强制为文本

            exam_number = data.get('考生号', '')
            if exam_number and exam_number != '未识别':
                exam_number = f"'{exam_number}"  # 在前面加单引号强制为文本

            # 构建数据行
            row = [
                filename,
                data.get('姓名', ''),
                data.get('性别', ''),
                id_number,
                exam_number,
                data.get('证书编号', ''),
                data.get('总分', ''),
                data.get('语文', ''),
                data.get('数学', ''),
                data.get('外语', ''),
                data.get('历史', ''),
                data.get('思想政治', ''),
                data.get('地理', ''),
                validation.get('calculated_total', ''),
                '通过' if validation.get('is_valid', False) else '失败',
                validation.get('difference', '')
            ]

            data_rows.append(row)

        return data_rows
    
    def create_basic_excel(self, results, output_path):
        """
        创建基础Excel文件
        
        Args:
            results (list): 处理结果列表
            output_path (str): 输出文件路径
        """
        try:
            # 准备数据
            data_rows = self.prepare_data(results)
            
            # 创建DataFrame
            df = pd.DataFrame(data_rows, columns=self.columns)
            
            # 导出Excel
            df.to_excel(output_path, index=False, engine='openpyxl')
            
            print(f"基础Excel文件已导出: {output_path}")
            
        except Exception as e:
            print(f"导出基础Excel失败: {str(e)}")
    
    def create_formatted_excel(self, results, output_path):
        """
        创建格式化的Excel文件
        
        Args:
            results (list): 处理结果列表
            output_path (str): 输出文件路径
        """
        try:
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "高考成绩统计"
            
            # 准备数据
            data_rows = self.prepare_data(results)
            
            # 写入标题行
            ws.append(self.columns)
            
            # 写入数据行
            for row in data_rows:
                ws.append(row)

            # 设置身份证号和考生号列为文本格式，避免科学计数法
            for row_num in range(2, len(data_rows) + 2):  # 从第2行开始（跳过标题）
                # 身份证号列（第4列）
                id_cell = ws.cell(row=row_num, column=4)
                if id_cell.value and str(id_cell.value) != '':
                    id_cell.value = str(id_cell.value)
                    id_cell.number_format = '@'  # 文本格式

                # 考生号列（第5列）
                exam_cell = ws.cell(row=row_num, column=5)
                if exam_cell.value and str(exam_cell.value) != '':
                    exam_cell.value = str(exam_cell.value)
                    exam_cell.number_format = '@'  # 文本格式
            
            # 设置样式
            self._apply_formatting(ws, len(data_rows))
            
            # 保存文件
            wb.save(output_path)
            
            print(f"格式化Excel文件已导出: {output_path}")
            
        except Exception as e:
            print(f"导出格式化Excel失败: {str(e)}")
    
    def _apply_formatting(self, worksheet, data_rows_count):
        """
        应用Excel格式化
        
        Args:
            worksheet: Excel工作表对象
            data_rows_count (int): 数据行数
        """
        # 定义样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        data_alignment = Alignment(horizontal="center", vertical="center")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 设置标题行样式
        for col in range(1, len(self.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        # 设置数据行样式
        for row in range(2, data_rows_count + 2):
            for col in range(1, len(self.columns) + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.alignment = data_alignment
                cell.border = border
                
                # 验证结果列特殊颜色
                if col == 15:  # 分数验证列
                    if cell.value == '失败':
                        cell.fill = PatternFill(start_color="FFE6E6", end_color="FFE6E6", fill_type="solid")
                    elif cell.value == '通过':
                        cell.fill = PatternFill(start_color="E6FFE6", end_color="E6FFE6", fill_type="solid")
        
        # 自动调整列宽
        for col in range(1, len(self.columns) + 1):
            column_letter = worksheet.cell(row=1, column=col).column_letter
            max_length = 0
            
            for row in range(1, data_rows_count + 2):
                cell_value = str(worksheet.cell(row=row, column=col).value or '')
                max_length = max(max_length, len(cell_value))
            
            # 设置列宽，最小10，最大30
            adjusted_width = min(max(max_length + 2, 10), 30)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def create_summary_sheet(self, results, output_path):
        """
        创建包含汇总信息的Excel文件
        
        Args:
            results (list): 处理结果列表
            output_path (str): 输出文件路径
        """
        try:
            # 创建工作簿
            wb = Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 创建数据工作表
            data_ws = wb.create_sheet("成绩数据")
            self._create_data_sheet(data_ws, results)
            
            # 创建汇总工作表
            summary_ws = wb.create_sheet("数据汇总")
            self._create_summary_sheet(summary_ws, results)
            
            # 保存文件
            wb.save(output_path)
            
            print(f"汇总Excel文件已导出: {output_path}")
            
        except Exception as e:
            print(f"导出汇总Excel失败: {str(e)}")
    
    def _create_data_sheet(self, worksheet, results):
        """创建数据工作表"""
        # 准备数据
        data_rows = self.prepare_data(results)
        
        # 写入标题行
        worksheet.append(self.columns)
        
        # 写入数据行
        for row in data_rows:
            worksheet.append(row)
        
        # 应用格式化
        self._apply_formatting(worksheet, len(data_rows))
    
    def _create_summary_sheet(self, worksheet, results):
        """创建汇总工作表"""
        # 统计信息
        total_images = len(results)
        valid_scores = sum(1 for r in results if r.get('validation', {}).get('is_valid', False))
        invalid_scores = total_images - valid_scores
        
        # 写入汇总信息
        summary_data = [
            ['高考成绩识别汇总报告'],
            [''],
            ['处理时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['总图片数', total_images],
            ['验证通过', valid_scores],
            ['验证失败', invalid_scores],
            ['通过率', f"{(valid_scores/total_images*100):.1f}%" if total_images > 0 else "0%"],
            [''],
            ['验证失败的文件:']
        ]
        
        # 添加验证失败的文件列表
        for result in results:
            if not result.get('validation', {}).get('is_valid', False):
                filename = os.path.basename(result.get('image_path', ''))
                difference = result.get('validation', {}).get('difference', 0)
                summary_data.append([filename, f"差值: {difference}"])
        
        # 写入数据
        for row in summary_data:
            worksheet.append(row)
        
        # 设置样式
        worksheet.cell(row=1, column=1).font = Font(bold=True, size=16)
        worksheet.column_dimensions['A'].width = 20
        worksheet.column_dimensions['B'].width = 30


def test_exporter():
    """测试Excel导出功能"""
    exporter = ExcelExporter()
    
    # 测试数据
    test_results = [
        {
            'image_path': 'imgs/test1.png',
            'data': {
                '姓名': '张三',
                '性别': '男',
                '身份证号': '123456789012345678',
                '考生号': '1234567890',
                '证书编号': 'ABC123',
                '总分': '638',
                '语文': '120',
                '数学': '130',
                '外语': '125',
                '历史': '85',
                '思想政治': '90',
                '地理': '88'
            },
            'validation': {
                'is_valid': True,
                'calculated_total': 638,
                'recognized_total': 638,
                'difference': 0
            }
        }
    ]
    
    # 测试导出
    exporter.create_summary_sheet(test_results, "test_output.xlsx")
    print("测试导出完成")


if __name__ == "__main__":
    test_exporter()
