"""
自动安装脚本
检查并安装项目所需的依赖包
"""

import subprocess
import sys
import os
import importlib


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: Python {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def check_pip():
    """检查pip是否可用"""
    try:
        import pip
        print("✅ pip可用")
        return True
    except ImportError:
        print("❌ 错误: pip不可用，请先安装pip")
        return False


def install_package(package_name):
    """
    安装单个包
    
    Args:
        package_name (str): 包名
        
    Returns:
        bool: 安装是否成功
    """
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败:")
        print(f"错误信息: {e.stderr}")
        return False


def check_package_installed(package_name, import_name=None):
    """
    检查包是否已安装
    
    Args:
        package_name (str): 包名
        import_name (str): 导入名（如果与包名不同）
        
    Returns:
        bool: 是否已安装
    """
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False


def install_requirements():
    """安装requirements.txt中的依赖"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"❌ 错误: 未找到 {requirements_file} 文件")
        return False
    
    print(f"从 {requirements_file} 安装依赖...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", requirements_file],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ 所有依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 依赖安装失败:")
        print(f"错误信息: {e.stderr}")
        return False


def verify_installation():
    """验证关键包是否正确安装"""
    print("\n验证安装...")
    
    packages_to_check = [
        ("easyocr", "easyocr"),
        ("torch", "torch"),
        ("torchvision", "torchvision"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("Pillow", "PIL"),
        ("opencv-python", "cv2"),
        ("numpy", "numpy")
    ]
    
    all_installed = True
    
    for package_name, import_name in packages_to_check:
        if check_package_installed(package_name, import_name):
            print(f"✅ {package_name} 已正确安装")
        else:
            print(f"❌ {package_name} 未正确安装")
            all_installed = False
    
    return all_installed


def test_easyocr():
    """测试EasyOCR是否能正常工作"""
    print("\n测试EasyOCR...")

    try:
        import easyocr
        print("✅ EasyOCR导入成功")

        # 尝试初始化OCR（这会下载模型）
        print("正在初始化EasyOCR模型（首次运行会下载模型文件）...")
        try:
            # 尝试GPU模式
            reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 先用CPU模式测试
            print("✅ EasyOCR初始化成功")
        except Exception as e:
            print(f"⚠️ EasyOCR初始化警告: {str(e)}")
            print("这可能是正常的，特别是在首次运行时")

        return True
    except Exception as e:
        print(f"❌ EasyOCR测试失败: {str(e)}")
        return False


def create_test_structure():
    """创建测试目录结构"""
    print("\n创建项目目录结构...")
    
    directories = ["imgs", "output", "logs"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"ℹ️ 目录已存在: {directory}")


def main():
    """主函数"""
    print("=" * 60)
    print("高考成绩单图片识别系统 - 自动安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查pip
    if not check_pip():
        return False
    
    # 安装依赖
    print("\n开始安装依赖包...")
    if not install_requirements():
        return False
    
    # 验证安装
    if not verify_installation():
        print("\n❌ 部分包安装失败，请检查错误信息")
        return False
    
    # 测试EasyOCR
    if not test_easyocr():
        print("\n⚠️ EasyOCR测试失败，但可能仍能正常工作")
    
    # 创建目录结构
    create_test_structure()
    
    print("\n" + "=" * 60)
    print("✅ 安装完成！")
    print("=" * 60)
    print("\n使用说明:")
    print("1. 将要处理的图片放入 'imgs' 文件夹")
    print("2. 运行命令: python main.py")
    print("3. 查看 'output' 文件夹中的结果")
    print("\n注意:")
    print("- 首次运行时EasyOCR会下载模型文件，需要网络连接")
    print("- 如果遇到问题，请查看 'logs' 文件夹中的日志文件")
    print("- EasyOCR对中文识别效果更好，支持GPU加速")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n安装过程中发生错误: {str(e)}")
        sys.exit(1)
