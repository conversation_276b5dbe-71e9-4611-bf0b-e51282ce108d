"""
高考成绩单图片识别主程序
识别imgs文件夹中的所有图片，提取成绩信息并导出Excel
"""

import os
import sys
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from ocr_processor import OCRProcessor
from data_extractor import DataExtractor
from excel_exporter import ExcelExporter
from utils import ConfigManager, Logger, ProgressTracker, ensure_directory, format_time_duration


class GaoKaoScoreProcessor:
    """高考成绩处理器主类"""

    def __init__(self, imgs_dir="imgs", output_dir="output", config_path="config.json"):
        """
        初始化处理器

        Args:
            imgs_dir (str): 图片文件夹路径
            output_dir (str): 输出文件夹路径
            config_path (str): 配置文件路径
        """
        self.imgs_dir = imgs_dir
        self.output_dir = output_dir

        # 初始化配置和日志
        self.config = ConfigManager(config_path)

        # 创建日志文件
        log_file = os.path.join(output_dir, f"processing_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        self.logger = Logger(log_file, enable_console=True)

        # 初始化各个组件
        self.ocr_processor = OCRProcessor(self.config, self.logger)
        self.data_extractor = DataExtractor(self.config, self.logger)
        self.excel_exporter = ExcelExporter()

        # 创建输出目录
        ensure_directory(output_dir)

        self.logger.info("高考成绩处理器初始化完成")
    
    def get_image_files(self):
        """
        获取所有图片文件
        
        Returns:
            list: 图片文件路径列表
        """
        if not os.path.exists(self.imgs_dir):
            print(f"错误: 图片文件夹 '{self.imgs_dir}' 不存在")
            return []
        
        image_files = []
        supported_extensions = ('.png', '.jpg', '.jpeg', '.PNG', '.JPG', '.JPEG')
        
        for filename in os.listdir(self.imgs_dir):
            if filename.endswith(supported_extensions):
                image_path = os.path.join(self.imgs_dir, filename)
                image_files.append(image_path)
        
        print(f"找到 {len(image_files)} 个图片文件")
        return sorted(image_files)
    
    def process_single_image(self, image_path):
        """
        处理单张图片

        Args:
            image_path (str): 图片路径

        Returns:
            dict: 处理结果
        """
        start_time = datetime.now()

        try:
            self.logger.info(f"开始处理图片: {os.path.basename(image_path)}")

            # OCR识别
            text = self.ocr_processor.get_all_text(image_path)

            if not text.strip():
                self.logger.warning(f"未能从图片中识别到文字: {os.path.basename(image_path)}")
                return {
                    'image_path': image_path,
                    'data': {},
                    'validation': {'is_valid': False, 'error': '未识别到文字'},
                    'ocr_text': '',
                    'processing_time': (datetime.now() - start_time).total_seconds()
                }

            # 数据提取
            result = self.data_extractor.process_image_data(text, image_path)
            result['ocr_text'] = text
            result['processing_time'] = (datetime.now() - start_time).total_seconds()

            # 数据验证增强
            self._enhance_validation(result)

            # 记录处理结果
            self.print_extraction_result(result)

            return result

        except Exception as e:
            self.logger.error(f"处理图片失败 {image_path}: {str(e)}")
            return {
                'image_path': image_path,
                'data': {},
                'validation': {'is_valid': False, 'error': str(e)},
                'ocr_text': '',
                'processing_time': (datetime.now() - start_time).total_seconds()
            }

    def _enhance_validation(self, result):
        """
        增强数据验证

        Args:
            result (dict): 处理结果
        """
        data = result.get('data', {})
        validation_errors = []

        # 验证姓名
        name_validation = self.data_extractor.validator.validate_name(data.get('姓名'))
        if not name_validation['valid']:
            validation_errors.append(f"姓名: {name_validation['error']}")

        # 验证性别
        gender_validation = self.data_extractor.validator.validate_gender(data.get('性别'))
        if not gender_validation['valid']:
            validation_errors.append(f"性别: {gender_validation['error']}")

        # 验证身份证号
        id_validation = self.data_extractor.validator.validate_id_number(data.get('身份证号'))
        if not id_validation['valid']:
            validation_errors.append(f"身份证号: {id_validation['error']}")

        # 验证考生号
        exam_validation = self.data_extractor.validator.validate_exam_number(data.get('考生号'))
        if not exam_validation['valid']:
            validation_errors.append(f"考生号: {exam_validation['error']}")

        # 验证各科成绩
        score_fields = ['语文', '数学', '外语', '历史', '思想政治', '地理']
        for field in score_fields:
            score_validation = self.data_extractor.validator.validate_score(data.get(field))
            if not score_validation['valid']:
                validation_errors.append(f"{field}: {score_validation['error']}")

        # 验证总分
        total_validation = self.data_extractor.validator.validate_score(data.get('总分'), 'total')
        if not total_validation['valid']:
            validation_errors.append(f"总分: {total_validation['error']}")

        # 更新验证结果
        if validation_errors:
            result['validation']['validation_errors'] = validation_errors
            result['validation']['data_quality'] = 'poor'
        else:
            result['validation']['data_quality'] = 'good'
    
    def print_extraction_result(self, result):
        """
        打印提取结果
        
        Args:
            result (dict): 处理结果
        """
        data = result.get('data', {})
        validation = result.get('validation', {})
        
        print("提取结果:")
        print(f"  姓名: {data.get('姓名', '未识别')}")
        print(f"  性别: {data.get('性别', '未识别')}")
        print(f"  身份证号: {data.get('身份证号', '未识别')}")
        print(f"  考生号: {data.get('考生号', '未识别')}")
        print(f"  证书编号: {data.get('证书编号', '未识别')}")
        print(f"  总分: {data.get('总分', '未识别')}")
        print(f"  语文: {data.get('语文', '未识别')}")
        print(f"  数学: {data.get('数学', '未识别')}")
        print(f"  外语: {data.get('外语', '未识别')}")
        print(f"  历史: {data.get('历史', '未识别')}")
        print(f"  思想政治: {data.get('思想政治', '未识别')}")
        print(f"  地理: {data.get('地理', '未识别')}")
        
        if validation.get('is_valid'):
            print(f"  ✓ 分数验证通过 (总分: {validation.get('recognized_total')})")
        else:
            print(f"  ✗ 分数验证失败")
            if 'calculated_total' in validation and 'recognized_total' in validation:
                print(f"    计算总分: {validation.get('calculated_total')}")
                print(f"    识别总分: {validation.get('recognized_total')}")
                print(f"    差值: {validation.get('difference', 0)}")
    
    def process_all_images(self):
        """
        处理所有图片

        Returns:
            list: 所有处理结果
        """
        image_files = self.get_image_files()

        if not image_files:
            self.logger.warning("没有找到可处理的图片文件")
            return []

        self.logger.info(f"开始处理 {len(image_files)} 个图片文件")
        print("=" * 60)

        # 获取处理设置
        max_workers = self.config.get("processing_settings.max_workers", 4)
        use_multithread = len(image_files) > 1 and max_workers > 1

        # 初始化进度跟踪器
        progress = ProgressTracker(len(image_files))

        results = []

        if use_multithread:
            self.logger.info(f"使用多线程处理 (工作线程数: {max_workers})")
            results = self._process_images_multithread(image_files, max_workers, progress)
        else:
            self.logger.info("使用单线程处理")
            results = self._process_images_singlethread(image_files, progress)

        print()  # 换行
        self.logger.info("所有图片处理完成")

        return results

    def _process_images_singlethread(self, image_files, progress):
        """
        单线程处理图片

        Args:
            image_files (list): 图片文件列表
            progress (ProgressTracker): 进度跟踪器

        Returns:
            list: 处理结果列表
        """
        results = []

        for image_path in image_files:
            result = self.process_single_image(image_path)
            results.append(result)

            progress.update()
            progress.print_progress(f"处理: {os.path.basename(image_path)}")

        return results

    def _process_images_multithread(self, image_files, max_workers, progress):
        """
        多线程处理图片

        Args:
            image_files (list): 图片文件列表
            max_workers (int): 最大工作线程数
            progress (ProgressTracker): 进度跟踪器

        Returns:
            list: 处理结果列表
        """
        results = [None] * len(image_files)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(self.process_single_image, image_path): i
                for i, image_path in enumerate(image_files)
            }

            # 收集结果
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result

                    progress.update()
                    filename = os.path.basename(result.get('image_path', ''))
                    progress.print_progress(f"完成: {filename}")

                except Exception as e:
                    self.logger.error(f"线程处理失败: {str(e)}")
                    results[index] = {
                        'image_path': image_files[index],
                        'data': {},
                        'validation': {'is_valid': False, 'error': f'线程处理失败: {str(e)}'},
                        'ocr_text': ''
                    }
                    progress.update()

        return results
    
    def export_results(self, results):
        """
        导出处理结果
        
        Args:
            results (list): 处理结果列表
        """
        if not results:
            print("没有结果可导出")
            return
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 导出详细Excel文件
        excel_path = os.path.join(self.output_dir, f"高考成绩统计_{timestamp}.xlsx")
        self.excel_exporter.create_summary_sheet(results, excel_path)
        
        # 导出OCR原始文本（用于调试）
        txt_path = os.path.join(self.output_dir, f"OCR原始文本_{timestamp}.txt")
        self.export_ocr_text(results, txt_path)
        
        print(f"\n导出完成:")
        print(f"  Excel文件: {excel_path}")
        print(f"  OCR文本: {txt_path}")
    
    def export_ocr_text(self, results, output_path):
        """
        导出OCR原始文本
        
        Args:
            results (list): 处理结果列表
            output_path (str): 输出文件路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                for result in results:
                    image_path = result.get('image_path', '')
                    ocr_text = result.get('ocr_text', '')
                    
                    f.write(f"{'='*60}\n")
                    f.write(f"文件: {os.path.basename(image_path)}\n")
                    f.write(f"{'='*60}\n")
                    f.write(ocr_text)
                    f.write(f"\n\n")
        
        except Exception as e:
            print(f"导出OCR文本失败: {str(e)}")
    
    def print_summary(self, results):
        """
        打印处理汇总
        
        Args:
            results (list): 处理结果列表
        """
        total_count = len(results)
        valid_count = sum(1 for r in results if r.get('validation', {}).get('is_valid', False))
        invalid_count = total_count - valid_count
        
        print("\n" + "="*50)
        print("处理汇总")
        print("="*50)
        print(f"总图片数: {total_count}")
        print(f"验证通过: {valid_count}")
        print(f"验证失败: {invalid_count}")
        print(f"通过率: {(valid_count/total_count*100):.1f}%" if total_count > 0 else "0%")
        
        if invalid_count > 0:
            print(f"\n验证失败的文件:")
            for result in results:
                if not result.get('validation', {}).get('is_valid', False):
                    filename = os.path.basename(result.get('image_path', ''))
                    error = result.get('validation', {}).get('error', '分数不匹配')
                    print(f"  - {filename}: {error}")
    
    def run(self):
        """运行主程序"""
        print("高考成绩单图片识别程序")
        print("="*50)
        
        try:
            # 处理所有图片
            results = self.process_all_images()
            
            if results:
                # 打印汇总
                self.print_summary(results)
                
                # 导出结果
                self.export_results(results)
                
                print(f"\n程序执行完成！")
            else:
                print("没有成功处理任何图片")
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序执行出错: {str(e)}")


def main():
    """主函数"""
    print("高考成绩单图片识别系统 v2.0")
    print("=" * 50)

    # 检查imgs文件夹是否存在
    if not os.path.exists("imgs"):
        print("❌ 错误: 未找到 'imgs' 文件夹")
        print("正在创建 'imgs' 文件夹...")
        os.makedirs("imgs")
        print("✅ 已创建 'imgs' 文件夹")
        print("请将要处理的图片放在 'imgs' 文件夹中，然后重新运行程序")
        return

    # 检查是否有图片文件
    image_files = []
    for file in os.listdir("imgs"):
        if file.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_files.append(file)

    if not image_files:
        print("⚠️ 警告: 'imgs' 文件夹中没有图片文件")
        print("请将要处理的图片（PNG、JPG格式）放入 'imgs' 文件夹")
        return

    print(f"✅ 找到 {len(image_files)} 个图片文件")

    try:
        # 创建处理器并运行
        processor = GaoKaoScoreProcessor()
        processor.run()

        print("\n🎉 程序执行完成！")
        print("请查看 'output' 文件夹中的结果文件")

    except KeyboardInterrupt:
        print("\n\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序执行出错: {str(e)}")
        print("请检查日志文件获取详细错误信息")


if __name__ == "__main__":
    main()
