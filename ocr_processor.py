"""
OCR图片识别处理模块
使用EasyOCR识别图片中的文字信息
"""

import os
import easyocr
import cv2
import numpy as np
from PIL import Image, ImageEnhance
from utils import ConfigManager, Logger


class OCRProcessor:
    """OCR处理器类"""

    def __init__(self, config_manager=None, logger=None):
        """
        初始化OCR处理器

        Args:
            config_manager (ConfigManager): 配置管理器
            logger (Logger): 日志记录器
        """
        self.config = config_manager or ConfigManager()
        self.logger = logger or Logger()

        # 从配置获取OCR设置
        ocr_settings = self.config.get("ocr_settings", {})

        try:
            # 初始化EasyOCR
            # 支持中文和英文识别
            languages = ['ch_sim', 'en']  # 简体中文和英文

            # 获取GPU设置
            use_gpu = ocr_settings.get("use_gpu", False)

            self.logger.info(f"正在初始化EasyOCR (GPU: {use_gpu})...")
            self.ocr = easyocr.Reader(languages, gpu=use_gpu)
            self.logger.info("EasyOCR初始化成功")

        except Exception as e:
            self.logger.error(f"EasyOCR初始化失败: {str(e)}")
            # 如果GPU初始化失败，尝试CPU模式
            try:
                self.logger.info("尝试使用CPU模式初始化EasyOCR...")
                self.ocr = easyocr.Reader(['ch_sim', 'en'], gpu=False)
                self.logger.info("EasyOCR使用CPU模式初始化成功")
            except Exception as e2:
                self.logger.error(f"EasyOCR CPU模式初始化也失败: {str(e2)}")
                raise e2
    
    def preprocess_image(self, image_path):
        """
        图片预处理，提高OCR识别准确率

        Args:
            image_path (str): 图片路径

        Returns:
            numpy.ndarray: 预处理后的图片数组
        """
        try:
            # 获取预处理配置
            preprocessing_config = self.config.get("image_preprocessing", {})

            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")

            # 调整图片大小 - 适度增加分辨率以提高OCR准确率
            resize_factor = preprocessing_config.get("resize_factor", 2.0)  # 默认放大2倍
            if resize_factor != 1.0:
                height, width = image.shape[:2]
                new_height = int(height * resize_factor)
                new_width = int(width * resize_factor)
                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 基于测试结果，使用最佳预处理组合：放大2倍 + 灰度化
            # 不使用其他复杂的预处理，避免过度处理

            # 去噪处理 - 关闭，避免丢失细节
            if preprocessing_config.get("enable_denoising", False):
                gray = cv2.fastNlMeansDenoising(gray)

            # 高斯模糊 - 关闭
            blur_kernel = preprocessing_config.get("gaussian_blur_kernel", 0)
            if blur_kernel > 0:
                gray = cv2.GaussianBlur(gray, (blur_kernel, blur_kernel), 0)

            # 对比度增强 - 关闭，避免过度处理
            if preprocessing_config.get("enable_contrast_enhancement", False):
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                gray = clahe.apply(gray)

            # 锐化处理 - 关闭
            if preprocessing_config.get("enable_sharpening", False):
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                gray = cv2.filter2D(gray, -1, kernel)

            # 形态学操作 - 关闭
            if preprocessing_config.get("enable_morphology", False):
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
                gray = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)

            return gray

        except Exception as e:
            self.logger.error(f"图片预处理失败 {image_path}: {str(e)}")
            # 如果预处理失败，返回原图的灰度版本
            try:
                original = cv2.imread(image_path)
                return cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
            except:
                return None
    
    def extract_text_from_image(self, image_path, retry_count=None):
        """
        从图片中提取文字信息

        Args:
            image_path (str): 图片路径
            retry_count (int): 重试次数

        Returns:
            list: 包含识别结果的列表，每个元素包含位置和文字信息
        """
        if retry_count is None:
            retry_count = self.config.get("processing_settings.retry_count", 2)

        for attempt in range(retry_count + 1):
            try:
                self.logger.info(f"正在处理图片: {os.path.basename(image_path)} (尝试 {attempt + 1}/{retry_count + 1})")

                # 预处理图片
                processed_image = self.preprocess_image(image_path)
                if processed_image is None:
                    raise ValueError("图片预处理失败")

                # 使用EasyOCR识别文字
                result = self.ocr.readtext(processed_image)

                # 提取文字信息
                text_results = []
                confidence_threshold = self.config.get("ocr_settings.confidence_threshold", 0.4)  # 适度降低阈值

                if result:
                    try:
                        # 处理EasyOCR的返回格式
                        # EasyOCR返回格式: [(bbox, text, confidence), ...]
                        for detection in result:
                            if len(detection) >= 3:
                                bbox = detection[0]  # 边界框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                                text = detection[1]  # 识别的文字
                                confidence = detection[2]  # 置信度

                                # 只保留置信度高于阈值的结果
                                if confidence >= confidence_threshold:
                                    text_results.append({
                                        'bbox': bbox,
                                        'text': text,
                                        'confidence': confidence
                                    })

                    except Exception as e:
                        self.logger.error(f"解析EasyOCR结果失败: {str(e)}")
                        self.logger.error(f"result类型: {type(result)}")
                        if hasattr(result, '__len__'):
                            self.logger.error(f"result长度: {len(result)}")
                        if isinstance(result, list) and len(result) > 0:
                            self.logger.error(f"result[0]类型: {type(result[0])}")
                            self.logger.error(f"result[0]内容: {result[0]}")
                        return []

                self.logger.info(f"识别到 {len(text_results)} 个高质量文字区域")
                return text_results

            except Exception as e:
                self.logger.warning(f"OCR识别失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == retry_count:
                    self.logger.error(f"OCR识别最终失败: {image_path}")
                    return []

        return []
    
    def get_all_text(self, image_path):
        """
        获取图片中的所有文字，按行排列
        
        Args:
            image_path (str): 图片路径
            
        Returns:
            str: 所有识别到的文字，用换行符分隔
        """
        text_results = self.extract_text_from_image(image_path)
        
        # 按y坐标排序，模拟从上到下的阅读顺序
        text_results.sort(key=lambda x: x['bbox'][0][1])
        
        all_text = []
        for result in text_results:
            if result['confidence'] > 0.5:  # 只保留置信度较高的结果
                all_text.append(result['text'])
        
        return '\n'.join(all_text)


def test_ocr():
    """测试OCR功能"""
    processor = OCRProcessor()
    
    # 测试imgs文件夹中的第一张图片
    imgs_dir = "imgs"
    if os.path.exists(imgs_dir):
        files = os.listdir(imgs_dir)
        image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        if image_files:
            test_image = os.path.join(imgs_dir, image_files[0])
            print(f"测试图片: {test_image}")
            
            text = processor.get_all_text(test_image)
            print("识别结果:")
            print(text)
        else:
            print("未找到图片文件")
    else:
        print("imgs文件夹不存在")


if __name__ == "__main__":
    test_ocr()
