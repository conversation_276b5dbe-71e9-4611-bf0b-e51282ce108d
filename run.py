"""
简化启动脚本
提供用户友好的启动界面
"""

import os
import sys
from datetime import datetime


def print_banner():
    """打印程序横幅"""
    print("=" * 70)
    print("                高考成绩单图片识别系统")
    print("                  OCR Score Recognition")
    print("=" * 70)
    print()


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查必要文件
    required_files = [
        "main.py",
        "ocr_processor.py", 
        "data_extractor.py",
        "excel_exporter.py",
        "utils.py",
        "config.json",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # 检查imgs目录
    if not os.path.exists("imgs"):
        print("⚠️ 未找到 'imgs' 文件夹，正在创建...")
        os.makedirs("imgs")
    
    # 检查imgs目录中的图片
    if os.path.exists("imgs"):
        image_files = []
        for file in os.listdir("imgs"):
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_files.append(file)
        
        if not image_files:
            print("⚠️ 'imgs' 文件夹中没有图片文件")
            print("   请将要处理的图片（PNG、JPG格式）放入 'imgs' 文件夹")
            return False
        else:
            print(f"✅ 找到 {len(image_files)} 个图片文件")
    
    # 检查Python包
    try:
        import paddleocr
        import pandas
        import openpyxl
        import cv2
        import numpy
        from PIL import Image
        print("✅ 所有依赖包已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖包: {str(e)}")
        print("   请运行: python install.py")
        return False
    
    return True


def show_menu():
    """显示菜单"""
    print("\n📋 请选择操作:")
    print("1. 开始处理图片")
    print("2. 查看配置信息")
    print("3. 查看帮助信息")
    print("4. 退出程序")
    print()


def show_config():
    """显示配置信息"""
    try:
        import json
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("\n⚙️ 当前配置:")
        print(f"OCR语言: {config.get('ocr_settings', {}).get('lang', 'ch')}")
        print(f"置信度阈值: {config.get('ocr_settings', {}).get('confidence_threshold', 0.5)}")
        print(f"最大工作线程: {config.get('processing_settings', {}).get('max_workers', 4)}")
        print(f"重试次数: {config.get('processing_settings', {}).get('retry_count', 2)}")
        print(f"图片预处理: {'启用' if config.get('image_preprocessing', {}).get('enable_preprocessing', True) else '禁用'}")
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {str(e)}")


def show_help():
    """显示帮助信息"""
    print("\n📖 帮助信息:")
    print()
    print("📁 文件夹说明:")
    print("   imgs/     - 放置要处理的图片文件（PNG、JPG格式）")
    print("   output/   - 处理结果输出文件夹")
    print("   logs/     - 日志文件存储文件夹")
    print()
    print("📊 识别字段:")
    print("   个人信息: 姓名、性别、身份证号、考生号、证书编号")
    print("   成绩信息: 总分、语文、数学、外语、历史、思想政治、地理")
    print()
    print("✅ 数据验证:")
    print("   - 自动验证总分是否等于各科成绩之和")
    print("   - 检查身份证号、考生号格式")
    print("   - 验证分数范围合理性")
    print()
    print("📋 输出文件:")
    print("   - Excel文件: 包含成绩数据和汇总统计")
    print("   - 日志文件: 详细的处理日志")
    print("   - OCR文本: 原始识别文本（调试用）")
    print()
    print("🔧 配置文件:")
    print("   config.json - 可自定义OCR设置、识别模式等")


def run_main_program():
    """运行主程序"""
    print("\n🚀 启动图片处理程序...")
    print("=" * 50)
    
    try:
        # 导入并运行主程序
        from main import main
        main()
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序运行出错: {str(e)}")
        print("请检查日志文件获取详细错误信息")


def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，程序无法运行")
        print("请解决上述问题后重新运行")
        input("\n按回车键退出...")
        return
    
    print("✅ 环境检查通过")
    
    # 主循环
    while True:
        show_menu()
        
        try:
            choice = input("请输入选项 (1-4): ").strip()
            
            if choice == "1":
                run_main_program()
                input("\n按回车键返回主菜单...")
                
            elif choice == "2":
                show_config()
                input("\n按回车键返回主菜单...")
                
            elif choice == "3":
                show_help()
                input("\n按回车键返回主菜单...")
                
            elif choice == "4":
                print("\n👋 感谢使用，再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序退出")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")


if __name__ == "__main__":
    main()
