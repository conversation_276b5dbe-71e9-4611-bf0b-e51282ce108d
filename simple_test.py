"""
简单的OCR测试
"""

import os
from paddleocr import PaddleOCR


def simple_test():
    """简单测试OCR"""
    print("初始化OCR...")
    ocr = PaddleOCR(lang='ch')
    
    # 找到第一张图片
    imgs_dir = "imgs"
    image_files = [f for f in os.listdir(imgs_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    test_image = os.path.join(imgs_dir, image_files[0])
    
    print(f"测试图片: {test_image}")
    
    try:
        # 使用predict方法
        print("使用predict方法...")
        result = ocr.predict(test_image)
        print("predict成功!")
        
        if result and len(result) > 0:
            ocr_result = result[0]
            print(f"OCRResult类型: {type(ocr_result)}")
            
            # 尝试不同的属性访问方式
            print("尝试访问OCRResult的内容...")

            # 尝试作为字典访问
            try:
                print("OCRResult keys:", list(ocr_result.keys()))

                if 'rec_texts' in ocr_result:
                    texts = ocr_result['rec_texts']
                    scores = ocr_result['rec_scores']
                    print(f"识别到 {len(texts)} 个文字:")
                    for i, (text, score) in enumerate(zip(texts[:5], scores[:5])):
                        print(f"  {i+1}. {text} (置信度: {score:.3f})")
                else:
                    print("OCRResult中没有rec_texts键")
                    print("可用的键:", list(ocr_result.keys())[:10])

            except Exception as e:
                print(f"作为字典访问失败: {str(e)}")

                # 尝试其他方法
                try:
                    print("尝试str()方法...")
                    text_str = str(ocr_result)
                    print(f"str()结果长度: {len(text_str)}")
                    print(f"str()前100字符: {text_str[:100]}")
                except Exception as e2:
                    print(f"str()方法失败: {str(e2)}")

                try:
                    print("尝试json()方法...")
                    json_result = ocr_result.json()
                    print(f"json()结果类型: {type(json_result)}")
                    if isinstance(json_result, dict):
                        print("json()结果的键:", list(json_result.keys())[:10])
                except Exception as e3:
                    print(f"json()方法失败: {str(e3)}")
        
    except Exception as e:
        print(f"predict方法失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        try:
            # 尝试ocr方法
            print("\n尝试ocr方法...")
            result = ocr.ocr(test_image)
            print("ocr方法成功!")
            print(f"result类型: {type(result)}")
            print(f"result长度: {len(result)}")
            
        except Exception as e2:
            print(f"ocr方法也失败: {str(e2)}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    simple_test()
