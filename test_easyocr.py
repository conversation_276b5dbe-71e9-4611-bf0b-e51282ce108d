"""
测试EasyOCR功能
"""

import os
import easyocr


def test_easyocr():
    """测试EasyOCR"""
    print("初始化EasyOCR...")
    
    try:
        # 初始化EasyOCR，支持中文和英文
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        print("✅ EasyOCR初始化成功")
    except Exception as e:
        print(f"❌ EasyOCR初始化失败: {str(e)}")
        return
    
    # 找到第一张图片
    imgs_dir = "imgs"
    if not os.path.exists(imgs_dir):
        print("❌ imgs文件夹不存在")
        return
    
    image_files = [f for f in os.listdir(imgs_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    if not image_files:
        print("❌ 没有找到图片文件")
        return
    
    test_image = os.path.join(imgs_dir, image_files[0])
    print(f"测试图片: {test_image}")
    
    try:
        print("开始OCR识别...")
        
        # 使用EasyOCR识别
        result = reader.readtext(test_image)
        
        print(f"✅ 识别成功！找到 {len(result)} 个文字区域")
        print("=" * 60)
        
        # 显示识别结果
        for i, (bbox, text, confidence) in enumerate(result):
            print(f"{i+1:2d}. {text:<30} (置信度: {confidence:.3f})")
        
        print("=" * 60)
        
        # 查找关键信息
        print("\n查找关键信息:")
        keywords = ['姓名', '性别', '身份证', '考生号', '总分', '语文', '数学', '外语', '历史', '政治', '地理']
        
        found_keywords = []
        for bbox, text, confidence in result:
            for keyword in keywords:
                if keyword in text:
                    found_keywords.append((keyword, text, confidence))
        
        if found_keywords:
            print("找到的关键信息:")
            for keyword, text, confidence in found_keywords:
                print(f"  {keyword}: {text} (置信度: {confidence:.3f})")
        else:
            print("未找到关键信息，显示所有识别结果:")
            for bbox, text, confidence in result[:10]:  # 只显示前10个
                print(f"  {text} (置信度: {confidence:.3f})")
        
        # 保存结果到文件
        with open("easyocr_test_result.txt", "w", encoding="utf-8") as f:
            f.write("EasyOCR识别结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"图片: {test_image}\n")
            f.write(f"识别到 {len(result)} 个文字区域\n\n")
            
            for i, (bbox, text, confidence) in enumerate(result):
                f.write(f"{i+1:2d}. {text} (置信度: {confidence:.3f})\n")
                f.write(f"    边界框: {bbox}\n\n")
        
        print(f"\n详细结果已保存到: easyocr_test_result.txt")
        
    except Exception as e:
        print(f"❌ OCR识别失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_easyocr()
