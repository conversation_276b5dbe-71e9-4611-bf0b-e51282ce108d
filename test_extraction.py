"""
测试数据提取功能
"""

import os
from utils import <PERSON>fig<PERSON>ana<PERSON>, Logger
from data_extractor import DataExtractor


def test_single_image():
    """测试单张图片的数据提取"""
    
    # 初始化组件
    config = ConfigManager()
    logger = Logger(enable_console=True)
    extractor = DataExtractor(config, logger)
    
    # 读取最新的OCR文本
    output_dir = "output"
    ocr_files = [f for f in os.listdir(output_dir) if f.startswith("OCR原始文本_")]
    if not ocr_files:
        print("❌ 未找到OCR文本文件")
        return
    
    latest_ocr_file = sorted(ocr_files)[-1]
    ocr_file_path = os.path.join(output_dir, latest_ocr_file)
    
    print(f"读取OCR文件: {ocr_file_path}")
    
    with open(ocr_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分割每个图片的内容
    sections = content.split("=" * 60)
    
    for i, section in enumerate(sections[1:], 1):  # 跳过第一个空段
        if not section.strip():
            continue
            
        lines = section.strip().split('\n')
        if len(lines) < 2:
            continue
            
        filename = lines[0].replace("文件: ", "").strip()
        text_content = '\n'.join(lines[2:])  # 跳过分隔线
        
        print(f"\n{'='*50}")
        print(f"测试图片: {filename}")
        print(f"{'='*50}")
        print("OCR文本:")
        print(text_content)
        print("\n" + "-"*30)
        
        # 测试数据提取
        result = extractor.process_image_data(text_content, filename)
        
        print("提取结果:")
        data = result.get('data', {})
        for key, value in data.items():
            status = "✅" if value and value != "未识别" else "❌"
            print(f"  {status} {key}: {value}")
        
        # 验证结果
        validation = result.get('validation', {})
        if validation.get('is_valid'):
            print(f"  ✅ 分数验证: 通过")
        else:
            print(f"  ❌ 分数验证: 失败")
            if 'calculated_total' in validation and 'recognized_total' in validation:
                print(f"     计算总分: {validation['calculated_total']}")
                print(f"     识别总分: {validation['recognized_total']}")
        
        print("\n" + "="*50)
        
        # 只测试前3个图片
        if i >= 3:
            break


def test_smart_extraction():
    """测试智能提取算法"""
    config = ConfigManager()
    logger = Logger(enable_console=False)
    extractor = DataExtractor(config, logger)
    
    # 测试用例
    test_cases = [
        {
            "name": "完整成绩单",
            "text": """全国普通高等学校招生统一考试
Transcripts
Certificate
贺德术
男
500229200505285714
24500118141142
P2245014012896
总分
271
语文
120
数学
85
外语
66""",
            "expected": {
                "姓名": "贺德术",
                "性别": "男",
                "身份证号": "500229200505285714",
                "考生号": "24500118141142",
                "证书编号": "P2245014012896",
                "总分": "271"
            }
        },
        {
            "name": "分离的文字",
            "text": """全国普通高等学校招生统一考试
Transcripts Certificate
500383200511151178
24500118183006
总分
外语
Form
CIII
301""",
            "expected": {
                "身份证号": "500383200511151178",
                "考生号": "24500118183006",
                "总分": "301"
            }
        }
    ]
    
    print("测试智能提取算法")
    print("="*60)
    
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        print("-"*40)
        
        result = extractor.process_image_data(test_case['text'], "test.png")
        data = result.get('data', {})
        
        print("提取结果:")
        for key, value in data.items():
            expected = test_case['expected'].get(key, '')
            if value and value != "未识别":
                if expected and value == expected:
                    print(f"  ✅ {key}: {value} (正确)")
                elif expected:
                    print(f"  ⚠️ {key}: {value} (期望: {expected})")
                else:
                    print(f"  ℹ️ {key}: {value}")
            elif expected:
                print(f"  ❌ {key}: 未识别 (期望: {expected})")


if __name__ == "__main__":
    print("数据提取测试")
    print("="*60)
    
    # 测试智能提取算法
    test_smart_extraction()
    
    print("\n" + "="*60)
    
    # 测试实际图片
    test_single_image()
