"""
测试系统改进效果
"""

import os
from utils import ConfigManager, Logger
from data_extractor import DataExtractor


def test_enhanced_extraction():
    """测试增强的提取算法"""
    config = ConfigManager()
    logger = Logger(enable_console=False)
    extractor = DataExtractor(config, logger)
    
    # 基于实际OCR结果的测试用例
    test_cases = [
        {
            "name": "典型成绩单1",
            "text": """全国普通高等学校招生统一考试
Transcripts
Certificate
500229200605285711
24500118141142
P2245014012896
总分
271
语文
120
数学
85
外语
66""",
            "expected_improvements": {
                "总分": "271",
                "语文": "120", 
                "数学": "85",
                "外语": "66"
            }
        },
        {
            "name": "分离文字测试",
            "text": """全国普通高等学校招生统一考试
500383200511151178
24500118183006
总分
301
外语
120
语文
90
数学
91""",
            "expected_improvements": {
                "总分": "301",
                "语文": "120",
                "数学": "91",
                "外语": "90"
            }
        },
        {
            "name": "复杂布局测试",
            "text": """Certificate
500393200505268195
总分
327
语
100
数
110
外语
117
历史
80
政治
85
地理
75""",
            "expected_improvements": {
                "总分": "327",
                "语文": "100",
                "数学": "110",
                "外语": "117",
                "历史": "80",
                "思想政治": "85",
                "地理": "75"
            }
        }
    ]
    
    print("测试增强的提取算法")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        print("-"*40)
        
        result = extractor.process_image_data(test_case['text'], "test.png")
        data = result.get('data', {})
        validation = result.get('validation', {})
        
        print("提取结果:")
        case_passed = 0
        case_total = 0
        
        for key, expected_value in test_case['expected_improvements'].items():
            actual_value = data.get(key, '')
            case_total += 1
            total_tests += 1
            
            if actual_value == expected_value:
                print(f"  ✅ {key}: {actual_value} (正确)")
                case_passed += 1
                passed_tests += 1
            else:
                print(f"  ❌ {key}: {actual_value} (期望: {expected_value})")
        
        # 显示其他识别到的字段
        other_fields = set(data.keys()) - set(test_case['expected_improvements'].keys())
        if other_fields:
            print("其他识别字段:")
            for field in other_fields:
                if data[field] and data[field] != '未识别':
                    print(f"  ℹ️ {field}: {data[field]}")
        
        # 验证结果
        if validation.get('is_valid'):
            print(f"  ✅ 分数验证: 通过")
        else:
            print(f"  ⚠️ 分数验证: 失败 (计算: {validation.get('calculated_total', 0)}, 识别: {validation.get('recognized_total', 0)})")
        
        print(f"本用例通过率: {case_passed}/{case_total} ({case_passed/case_total*100:.1f}%)")
    
    print("\n" + "="*60)
    print("总体测试结果")
    print("="*60)
    print(f"总通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    return passed_tests/total_tests if total_tests > 0 else 0


def compare_before_after():
    """对比改进前后的效果"""
    print("\n" + "="*60)
    print("改进效果对比")
    print("="*60)
    
    # 读取最新的两个处理结果进行对比
    output_dir = "output"
    excel_files = [f for f in os.listdir(output_dir) if f.startswith("高考成绩统计_") and f.endswith(".xlsx")]
    
    if len(excel_files) >= 2:
        latest_files = sorted(excel_files)[-2:]
        print(f"对比文件:")
        print(f"  之前: {latest_files[0]}")
        print(f"  最新: {latest_files[1]}")
        
        try:
            import pandas as pd
            
            # 读取两个文件
            df_before = pd.read_excel(os.path.join(output_dir, latest_files[0]), sheet_name="成绩数据")
            df_after = pd.read_excel(os.path.join(output_dir, latest_files[1]), sheet_name="成绩数据")
            
            print("\n字段识别率对比:")
            fields = ['身份证号', '考生号', '总分', '语文', '数学', '外语', '历史', '思想政治', '地理']
            
            for field in fields:
                if field in df_before.columns and field in df_after.columns:
                    before_count = len(df_before[(df_before[field].notna()) & (df_before[field] != '') & (df_before[field] != '未识别')])
                    after_count = len(df_after[(df_after[field].notna()) & (df_after[field] != '') & (df_after[field] != '未识别')])
                    
                    before_rate = (before_count / len(df_before)) * 100
                    after_rate = (after_count / len(df_after)) * 100
                    improvement = after_rate - before_rate
                    
                    status = "📈" if improvement > 0 else "📉" if improvement < 0 else "➡️"
                    print(f"  {status} {field}: {before_rate:.1f}% → {after_rate:.1f}% ({improvement:+.1f}%)")
            
            # 验证通过率对比
            if '分数验证' in df_before.columns and '分数验证' in df_after.columns:
                before_pass = len(df_before[df_before['分数验证'] == '通过'])
                after_pass = len(df_after[df_after['分数验证'] == '通过'])
                
                before_pass_rate = (before_pass / len(df_before)) * 100
                after_pass_rate = (after_pass / len(df_after)) * 100
                improvement = after_pass_rate - before_pass_rate
                
                print(f"\n验证通过率对比:")
                print(f"  📊 之前: {before_pass_rate:.1f}% ({before_pass}/{len(df_before)})")
                print(f"  📊 最新: {after_pass_rate:.1f}% ({after_pass}/{len(df_after)})")
                print(f"  📈 改进: {improvement:+.1f}%")
        
        except Exception as e:
            print(f"对比分析失败: {str(e)}")
    else:
        print("需要至少两个处理结果文件才能进行对比")


if __name__ == "__main__":
    print("系统改进效果测试")
    print("="*60)
    
    # 测试增强算法
    test_score = test_enhanced_extraction()
    
    # 对比改进效果
    compare_before_after()
    
    print(f"\n🎯 测试总结:")
    print(f"算法测试通过率: {test_score*100:.1f}%")
    
    if test_score >= 0.8:
        print("✅ 算法改进效果优秀！")
    elif test_score >= 0.6:
        print("⚠️ 算法改进效果良好，还有提升空间")
    else:
        print("❌ 算法需要进一步优化")
