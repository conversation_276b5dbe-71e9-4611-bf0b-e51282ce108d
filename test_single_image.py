"""
测试单张图片的OCR识别效果
专门用于调试和优化OCR参数
"""

import os
import cv2
import numpy as np
import easyocr
from PIL import Image, ImageEnhance

def test_different_preprocessing(image_path):
    """测试不同的预处理方法"""
    
    print(f"测试图片: {image_path}")
    print("=" * 60)
    
    # 读取原图
    original = cv2.imread(image_path)
    if original is None:
        print("无法读取图片")
        return
    
    # 初始化OCR
    reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
    
    # 测试1: 原图直接识别
    print("测试1: 原图直接识别")
    result1 = reader.readtext(original)
    print_ocr_result(result1)
    
    # 测试2: 灰度化
    print("\n测试2: 灰度化")
    gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
    result2 = reader.readtext(gray)
    print_ocr_result(result2)
    
    # 测试3: 放大2倍 + 灰度化
    print("\n测试3: 放大2倍 + 灰度化")
    height, width = original.shape[:2]
    resized = cv2.resize(original, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    gray_resized = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
    result3 = reader.readtext(gray_resized)
    print_ocr_result(result3)
    
    # 测试4: 放大2倍 + 灰度化 + 对比度增强
    print("\n测试4: 放大2倍 + 灰度化 + 对比度增强")
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(gray_resized)
    result4 = reader.readtext(enhanced)
    print_ocr_result(result4)
    
    # 测试5: 放大2倍 + 灰度化 + 对比度增强 + 锐化
    print("\n测试5: 放大2倍 + 灰度化 + 对比度增强 + 锐化")
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(enhanced, -1, kernel)
    result5 = reader.readtext(sharpened)
    print_ocr_result(result5)
    
    # 测试6: 二值化
    print("\n测试6: 二值化")
    _, binary = cv2.threshold(gray_resized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    result6 = reader.readtext(binary)
    print_ocr_result(result6)
    
    # 保存预处理后的图片用于查看
    cv2.imwrite("debug_gray_resized.png", gray_resized)
    cv2.imwrite("debug_enhanced.png", enhanced)
    cv2.imwrite("debug_sharpened.png", sharpened)
    cv2.imwrite("debug_binary.png", binary)
    
    print(f"\n预处理图片已保存，可查看效果")

def print_ocr_result(result):
    """打印OCR识别结果"""
    if not result:
        print("  未识别到任何文字")
        return
    
    print(f"  识别到 {len(result)} 个文字区域:")
    for i, (bbox, text, confidence) in enumerate(result):
        print(f"    {i+1}. '{text}' (置信度: {confidence:.3f})")
    
    # 按行排列显示
    sorted_result = sorted(result, key=lambda x: x[0][0][1])  # 按y坐标排序
    all_text = [item[1] for item in sorted_result if item[2] > 0.3]
    print(f"  按行排列: {' | '.join(all_text)}")

def extract_data_from_text(text_list):
    """从识别的文本中提取数据"""
    import re
    
    data = {}
    text = ' '.join(text_list)
    
    print(f"\n数据提取分析:")
    print(f"完整文本: {text}")
    
    # 查找身份证号
    id_match = re.search(r'(\d{15,18})', text)
    if id_match:
        data['身份证号'] = id_match.group(1)
        print(f"身份证号: {data['身份证号']}")
    
    # 查找考生号
    exam_match = re.search(r'(\d{8,15})', text)
    if exam_match and exam_match.group(1) != data.get('身份证号'):
        data['考生号'] = exam_match.group(1)
        print(f"考生号: {data['考生号']}")
    
    # 查找分数
    scores = re.findall(r'\b(\d{1,3})\b', text)
    scores = [int(s) for s in scores if 0 <= int(s) <= 750]
    if scores:
        print(f"发现的分数: {scores}")
        # 最大的可能是总分
        if scores:
            data['总分'] = max(scores)
            print(f"推测总分: {data['总分']}")
    
    # 查找证书编号
    cert_match = re.search(r'(P[A-Z]?\d{10,15})', text)
    if cert_match:
        data['证书编号'] = cert_match.group(1)
        print(f"证书编号: {data['证书编号']}")
    
    return data

if __name__ == "__main__":
    # 测试file0010_00.png
    test_image = "imgs/file0010_00.png"
    
    if os.path.exists(test_image):
        test_different_preprocessing(test_image)
    else:
        print(f"图片文件不存在: {test_image}")
