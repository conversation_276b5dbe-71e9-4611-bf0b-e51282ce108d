"""
系统测试脚本
测试各个模块的功能是否正常
"""

import os
import sys
import json
from datetime import datetime


def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试第三方库
        import paddleocr
        import pandas
        import openpyxl
        import cv2
        import numpy
        from PIL import Image
        print("✅ 第三方库导入成功")
        
        # 测试项目模块
        from utils import ConfigManager, DataValidator, Logger, ProgressTracker
        from ocr_processor import OCRProcessor
        from data_extractor import DataExtractor
        from excel_exporter import ExcelExporter
        print("✅ 项目模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {str(e)}")
        return False


def test_config():
    """测试配置管理"""
    print("\n🔍 测试配置管理...")
    
    try:
        from utils import ConfigManager
        
        # 测试配置加载
        config = ConfigManager("config.json")
        
        # 测试配置读取
        ocr_lang = config.get("ocr_settings.lang", "ch")
        confidence = config.get("ocr_settings.confidence_threshold", 0.5)
        
        print(f"✅ 配置加载成功 (语言: {ocr_lang}, 置信度: {confidence})")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        return False


def test_data_validator():
    """测试数据验证器"""
    print("\n🔍 测试数据验证器...")
    
    try:
        from utils import ConfigManager, DataValidator
        
        config = ConfigManager()
        validator = DataValidator(config)
        
        # 测试姓名验证
        name_result = validator.validate_name("张三")
        assert name_result['valid'], "姓名验证失败"
        
        # 测试性别验证
        gender_result = validator.validate_gender("男")
        assert gender_result['valid'], "性别验证失败"
        
        # 测试分数验证
        score_result = validator.validate_score("120")
        assert score_result['valid'], "分数验证失败"
        
        print("✅ 数据验证器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据验证器测试失败: {str(e)}")
        return False


def test_data_extractor():
    """测试数据提取器"""
    print("\n🔍 测试数据提取器...")
    
    try:
        from utils import ConfigManager, Logger
        from data_extractor import DataExtractor
        
        config = ConfigManager()
        logger = Logger(enable_console=False)
        extractor = DataExtractor(config, logger)
        
        # 测试文本
        test_text = """
        高考成绩通知单
        姓名：张三
        性别：男
        身份证号：123456789012345678
        考生号：1234567890
        证书编号：ABC123456
        
        成绩：
        语文：120
        数学：130
        外语：125
        历史：85
        思想政治：90
        地理：88
        总分：638
        """
        
        # 测试数据提取
        result = extractor.process_image_data(test_text, "test.png")
        
        # 验证提取结果
        data = result.get('data', {})
        assert data.get('姓名') == '张三', "姓名提取失败"
        assert data.get('性别') == '男', "性别提取失败"
        assert data.get('总分') == '638', "总分提取失败"
        
        # 验证分数验证
        validation = result.get('validation', {})
        assert validation.get('is_valid'), "分数验证失败"
        
        print("✅ 数据提取器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据提取器测试失败: {str(e)}")
        return False


def test_excel_exporter():
    """测试Excel导出器"""
    print("\n🔍 测试Excel导出器...")
    
    try:
        from excel_exporter import ExcelExporter
        
        exporter = ExcelExporter()
        
        # 测试数据
        test_results = [
            {
                'image_path': 'test.png',
                'data': {
                    '姓名': '张三',
                    '性别': '男',
                    '身份证号': '123456789012345678',
                    '考生号': '1234567890',
                    '证书编号': 'ABC123',
                    '总分': '638',
                    '语文': '120',
                    '数学': '130',
                    '外语': '125',
                    '历史': '85',
                    '思想政治': '90',
                    '地理': '88'
                },
                'validation': {
                    'is_valid': True,
                    'calculated_total': 638,
                    'recognized_total': 638,
                    'difference': 0
                }
            }
        ]
        
        # 测试Excel导出
        test_output = "test_output.xlsx"
        exporter.create_summary_sheet(test_results, test_output)
        
        # 检查文件是否生成
        assert os.path.exists(test_output), "Excel文件未生成"
        
        # 清理测试文件
        os.remove(test_output)
        
        print("✅ Excel导出器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Excel导出器测试失败: {str(e)}")
        return False


def test_progress_tracker():
    """测试进度跟踪器"""
    print("\n🔍 测试进度跟踪器...")
    
    try:
        from utils import ProgressTracker
        
        # 创建进度跟踪器
        progress = ProgressTracker(10)
        
        # 测试进度更新
        for i in range(5):
            progress.update()
        
        # 获取进度信息
        info = progress.get_progress_info()
        assert info['current'] == 5, "进度更新失败"
        assert info['percentage'] == 50.0, "进度百分比计算错误"
        
        print("✅ 进度跟踪器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 进度跟踪器测试失败: {str(e)}")
        return False


def test_directory_structure():
    """测试目录结构"""
    print("\n🔍 测试目录结构...")
    
    required_files = [
        "main.py",
        "ocr_processor.py",
        "data_extractor.py", 
        "excel_exporter.py",
        "utils.py",
        "config.json",
        "requirements.txt",
        "README.md"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 目录结构检查通过")
    return True


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("高考成绩单图片识别系统 - 功能测试")
    print("=" * 60)
    
    tests = [
        ("目录结构", test_directory_structure),
        ("模块导入", test_imports),
        ("配置管理", test_config),
        ("数据验证器", test_data_validator),
        ("数据提取器", test_data_extractor),
        ("Excel导出器", test_excel_exporter),
        ("进度跟踪器", test_progress_tracker)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 总计: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有测试通过！系统功能正常")
        return True
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    try:
        success = run_all_tests()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
