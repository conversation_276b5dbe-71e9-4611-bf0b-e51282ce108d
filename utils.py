"""
工具函数模块
提供通用的工具函数和数据验证功能
"""

import json
import re
import os
from datetime import datetime


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path="config.json"):
        """
        初始化配置管理器
        
        Args:
            config_path (str): 配置文件路径
        """
        self.config_path = config_path
        self.config = self.load_config()
    
    def load_config(self):
        """
        加载配置文件
        
        Returns:
            dict: 配置字典
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"警告: 配置文件 {self.config_path} 不存在，使用默认配置")
                return self.get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}，使用默认配置")
            return self.get_default_config()
    
    def get_default_config(self):
        """
        获取默认配置
        
        Returns:
            dict: 默认配置字典
        """
        return {
            "ocr_settings": {
                "use_angle_cls": True,
                "lang": "ch",
                "confidence_threshold": 0.5
            },
            "validation_rules": {
                "score_range": {"min": 0, "max": 150},
                "total_score_range": {"min": 0, "max": 900}
            }
        }
    
    def get(self, key_path, default=None):
        """
        获取配置值
        
        Args:
            key_path (str): 配置键路径，如 "ocr_settings.lang"
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default


class DataValidator:
    """数据验证器"""
    
    def __init__(self, config_manager):
        """
        初始化数据验证器
        
        Args:
            config_manager (ConfigManager): 配置管理器
        """
        self.config = config_manager
    
    def validate_id_number(self, id_number):
        """
        验证身份证号
        
        Args:
            id_number (str): 身份证号
            
        Returns:
            dict: 验证结果
        """
        if not id_number:
            return {"valid": False, "error": "身份证号为空"}
        
        # 去除空格
        id_number = id_number.strip()
        
        # 检查长度
        valid_lengths = self.config.get("validation_rules.id_number_length", [15, 18])
        if len(id_number) not in valid_lengths:
            return {"valid": False, "error": f"身份证号长度不正确，应为{valid_lengths}位"}
        
        # 检查格式
        if len(id_number) == 18:
            pattern = r'^[0-9]{17}[0-9X]$'
        else:
            pattern = r'^[0-9]{15}$'
        
        if not re.match(pattern, id_number):
            return {"valid": False, "error": "身份证号格式不正确"}
        
        return {"valid": True, "error": None}
    
    def validate_exam_number(self, exam_number):
        """
        验证考生号
        
        Args:
            exam_number (str): 考生号
            
        Returns:
            dict: 验证结果
        """
        if not exam_number:
            return {"valid": False, "error": "考生号为空"}
        
        exam_number = exam_number.strip()
        
        # 检查长度
        valid_lengths = self.config.get("validation_rules.exam_number_length", [8, 15])
        min_len, max_len = min(valid_lengths), max(valid_lengths)
        
        if not (min_len <= len(exam_number) <= max_len):
            return {"valid": False, "error": f"考生号长度不正确，应为{min_len}-{max_len}位"}
        
        # 检查是否全为数字
        if not exam_number.isdigit():
            return {"valid": False, "error": "考生号应为纯数字"}
        
        return {"valid": True, "error": None}
    
    def validate_score(self, score, score_type="subject"):
        """
        验证分数
        
        Args:
            score (str): 分数
            score_type (str): 分数类型 ("subject" 或 "total")
            
        Returns:
            dict: 验证结果
        """
        if not score:
            return {"valid": False, "error": "分数为空"}
        
        try:
            score_int = int(score)
        except ValueError:
            return {"valid": False, "error": "分数格式不正确"}
        
        # 获取分数范围
        if score_type == "total":
            score_range = self.config.get("validation_rules.total_score_range", {"min": 0, "max": 900})
        else:
            score_range = self.config.get("validation_rules.score_range", {"min": 0, "max": 150})
        
        min_score = score_range.get("min", 0)
        max_score = score_range.get("max", 150)
        
        if not (min_score <= score_int <= max_score):
            return {"valid": False, "error": f"分数超出范围({min_score}-{max_score})"}
        
        return {"valid": True, "error": None, "value": score_int}
    
    def validate_name(self, name):
        """
        验证姓名
        
        Args:
            name (str): 姓名
            
        Returns:
            dict: 验证结果
        """
        if not name:
            return {"valid": False, "error": "姓名为空"}
        
        name = name.strip()
        
        # 检查长度
        if len(name) < 2 or len(name) > 10:
            return {"valid": False, "error": "姓名长度不合理"}
        
        # 检查是否包含中文字符
        if not re.search(r'[\u4e00-\u9fff]', name):
            return {"valid": False, "error": "姓名应包含中文字符"}
        
        return {"valid": True, "error": None}
    
    def validate_gender(self, gender):
        """
        验证性别
        
        Args:
            gender (str): 性别
            
        Returns:
            dict: 验证结果
        """
        if not gender:
            return {"valid": False, "error": "性别为空"}
        
        gender = gender.strip()
        
        if gender not in ['男', '女']:
            return {"valid": False, "error": "性别应为'男'或'女'"}
        
        return {"valid": True, "error": None}


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total_items):
        """
        初始化进度跟踪器
        
        Args:
            total_items (int): 总项目数
        """
        self.total_items = total_items
        self.current_item = 0
        self.start_time = datetime.now()
    
    def update(self, increment=1):
        """
        更新进度
        
        Args:
            increment (int): 增量
        """
        self.current_item += increment
    
    def get_progress_info(self):
        """
        获取进度信息
        
        Returns:
            dict: 进度信息
        """
        if self.total_items == 0:
            percentage = 100
        else:
            percentage = (self.current_item / self.total_items) * 100
        
        elapsed_time = datetime.now() - self.start_time
        
        if self.current_item > 0:
            avg_time_per_item = elapsed_time.total_seconds() / self.current_item
            remaining_items = self.total_items - self.current_item
            estimated_remaining_time = avg_time_per_item * remaining_items
        else:
            estimated_remaining_time = 0
        
        return {
            "current": self.current_item,
            "total": self.total_items,
            "percentage": percentage,
            "elapsed_seconds": elapsed_time.total_seconds(),
            "estimated_remaining_seconds": estimated_remaining_time
        }
    
    def print_progress(self, message=""):
        """
        打印进度信息
        
        Args:
            message (str): 附加消息
        """
        info = self.get_progress_info()
        
        # 创建进度条
        bar_length = 30
        filled_length = int(bar_length * info["percentage"] / 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        
        # 格式化时间
        elapsed_min = int(info["elapsed_seconds"] // 60)
        elapsed_sec = int(info["elapsed_seconds"] % 60)
        
        remaining_min = int(info["estimated_remaining_seconds"] // 60)
        remaining_sec = int(info["estimated_remaining_seconds"] % 60)
        
        progress_text = (
            f"\r[{bar}] {info['percentage']:.1f}% "
            f"({info['current']}/{info['total']}) "
            f"已用时: {elapsed_min:02d}:{elapsed_sec:02d} "
            f"预计剩余: {remaining_min:02d}:{remaining_sec:02d}"
        )
        
        if message:
            progress_text += f" | {message}"
        
        print(progress_text, end="", flush=True)


class Logger:
    """日志记录器"""
    
    def __init__(self, log_file=None, enable_console=True):
        """
        初始化日志记录器
        
        Args:
            log_file (str): 日志文件路径
            enable_console (bool): 是否启用控制台输出
        """
        self.log_file = log_file
        self.enable_console = enable_console
        
        if self.log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
    
    def log(self, level, message):
        """
        记录日志
        
        Args:
            level (str): 日志级别
            message (str): 日志消息
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        
        # 控制台输出
        if self.enable_console:
            if level == "ERROR":
                print(f"❌ {message}")
            elif level == "WARNING":
                print(f"⚠️ {message}")
            elif level == "INFO":
                print(f"ℹ️ {message}")
            else:
                print(message)
        
        # 文件输出
        if self.log_file:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(log_message + '\n')
            except Exception as e:
                print(f"写入日志文件失败: {str(e)}")
    
    def info(self, message):
        """记录信息日志"""
        self.log("INFO", message)
    
    def warning(self, message):
        """记录警告日志"""
        self.log("WARNING", message)
    
    def error(self, message):
        """记录错误日志"""
        self.log("ERROR", message)


def format_time_duration(seconds):
    """
    格式化时间持续时间
    
    Args:
        seconds (float): 秒数
        
    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}分{secs}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}小时{minutes}分钟"


def ensure_directory(directory):
    """
    确保目录存在
    
    Args:
        directory (str): 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")


def get_file_size_mb(file_path):
    """
    获取文件大小（MB）
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        float: 文件大小（MB）
    """
    try:
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)
    except OSError:
        return 0.0
