@echo off
chcp 65001 >nul
title 高考成绩单图片识别系统

echo.
echo ========================================
echo     高考成绩单图片识别系统
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装

REM 检查是否首次运行
if not exist "output" (
    echo.
    echo 🔧 检测到首次运行，正在初始化...
    echo.
    
    REM 运行安装脚本
    python install.py
    if errorlevel 1 (
        echo.
        echo ❌ 初始化失败
        pause
        exit /b 1
    )
)

REM 检查imgs文件夹
if not exist "imgs" (
    mkdir imgs
    echo ✅ 已创建imgs文件夹
)

REM 检查imgs文件夹中是否有图片
set "has_images=0"
for %%f in (imgs\*.png imgs\*.jpg imgs\*.jpeg imgs\*.PNG imgs\*.JPG imgs\*.JPEG) do (
    set "has_images=1"
    goto :found_images
)

:found_images
if "%has_images%"=="0" (
    echo.
    echo ⚠️ 警告: imgs文件夹中没有图片文件
    echo 请将要处理的图片放入imgs文件夹，然后重新运行此程序
    echo.
    echo 按任意键打开imgs文件夹...
    pause >nul
    explorer imgs
    exit /b 0
)

echo ✅ 找到图片文件

REM 运行主程序
echo.
echo 🚀 启动程序...
echo.
python run.py

echo.
echo 程序已结束
pause
