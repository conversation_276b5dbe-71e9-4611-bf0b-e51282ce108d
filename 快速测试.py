"""
快速测试脚本 - 验证系统在真实数据上的表现
"""

import os
import time
from datetime import datetime


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🧪 高考成绩单图片识别系统 - 快速测试")
    print("=" * 60)
    print("🎯 验证系统在真实数据上的表现")
    print("=" * 60)


def check_test_environment():
    """检查测试环境"""
    print("\n🔍 检查测试环境...")
    
    # 检查imgs文件夹
    if not os.path.exists('imgs'):
        print("❌ imgs文件夹不存在")
        return False
    
    # 检查图片文件
    img_files = [f for f in os.listdir('imgs') if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    if not img_files:
        print("❌ imgs文件夹中没有图片文件")
        print("   请将真实的高考成绩单图片放入imgs文件夹中")
        return False
    
    print(f"✅ 找到 {len(img_files)} 个图片文件:")
    for i, img in enumerate(img_files[:5], 1):  # 只显示前5个
        print(f"   {i}. {img}")
    if len(img_files) > 5:
        print(f"   ... 还有 {len(img_files) - 5} 个文件")
    
    return True


def run_quick_test():
    """运行快速测试"""
    print("\n🚀 开始快速测试...")
    print("=" * 40)
    
    start_time = time.time()
    
    try:
        # 导入并运行主程序
        from main import main
        main()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print("\n" + "=" * 40)
        print("⏱️ 测试完成!")
        print(f"总处理时间: {processing_time:.1f} 秒")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def analyze_results():
    """分析测试结果"""
    print("\n📊 分析测试结果...")
    
    # 查找最新的输出文件
    output_dir = "output"
    if not os.path.exists(output_dir):
        print("❌ 没有找到输出文件夹")
        return
    
    # 查找最新的Excel文件
    excel_files = [f for f in os.listdir(output_dir) if f.startswith("高考成绩统计_") and f.endswith(".xlsx")]
    
    if not excel_files:
        print("❌ 没有找到处理结果文件")
        return
    
    latest_excel = sorted(excel_files)[-1]
    excel_path = os.path.join(output_dir, latest_excel)
    
    print(f"📄 结果文件: {latest_excel}")
    
    try:
        import pandas as pd
        df = pd.read_excel(excel_path, sheet_name="成绩数据")
        
        print(f"\n📈 处理统计:")
        print(f"   总记录数: {len(df)}")
        
        # 统计验证通过率
        if '分数验证' in df.columns:
            pass_count = len(df[df['分数验证'] == '通过'])
            fail_count = len(df[df['分数验证'] == '失败'])
            pass_rate = (pass_count / len(df)) * 100
            
            print(f"   验证通过: {pass_count}")
            print(f"   验证失败: {fail_count}")
            print(f"   通过率: {pass_rate:.1f}%")
            
            if pass_rate >= 90:
                print("   🎉 优秀! 通过率≥90%")
            elif pass_rate >= 70:
                print("   ✅ 良好! 通过率≥70%")
            elif pass_rate >= 50:
                print("   ⚠️ 一般! 通过率≥50%")
            else:
                print("   ❌ 需要改进! 通过率<50%")
        
        # 统计关键字段识别率
        print(f"\n🎯 关键字段识别率:")
        key_fields = ['身份证号', '考生号', '总分', '证书编号']
        
        for field in key_fields:
            if field in df.columns:
                valid_count = len(df[(df[field].notna()) & (df[field] != '') & (df[field] != '未识别')])
                rate = (valid_count / len(df)) * 100
                
                if rate >= 80:
                    status = "🎯"
                elif rate >= 60:
                    status = "✅"
                elif rate >= 40:
                    status = "⚠️"
                else:
                    status = "❌"
                
                print(f"   {status} {field}: {valid_count}/{len(df)} ({rate:.1f}%)")
        
        # 显示失败的文件
        if '分数验证' in df.columns:
            failed_files = df[df['分数验证'] == '失败']['图片文件名'].tolist()
            if failed_files:
                print(f"\n⚠️ 验证失败的文件:")
                for i, file in enumerate(failed_files[:5], 1):  # 只显示前5个
                    print(f"   {i}. {file}")
                if len(failed_files) > 5:
                    print(f"   ... 还有 {len(failed_files) - 5} 个文件")
        
        print(f"\n💾 完整结果文件: {excel_path}")
        
    except Exception as e:
        print(f"❌ 分析结果失败: {str(e)}")


def show_recommendations():
    """显示优化建议"""
    print("\n💡 优化建议:")
    print("=" * 30)
    print("1. 📸 图片质量:")
    print("   • 确保图片清晰，分辨率≥300DPI")
    print("   • 避免模糊、倾斜或光线不足")
    print("   • 使用PNG或高质量JPG格式")
    print()
    print("2. ⚙️ 系统配置:")
    print("   • 如果识别率低，可降低置信度阈值")
    print("   • 编辑config.json调整参数")
    print("   • 启用图片预处理提高识别率")
    print()
    print("3. 🔍 问题排查:")
    print("   • 查看logs文件夹中的日志")
    print("   • 检查OCR原始文本文件")
    print("   • 运行python test_system.py检查系统")


def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_test_environment():
        print("\n❌ 测试环境检查失败")
        print("请确保imgs文件夹中有真实的高考成绩单图片")
        input("\n按回车键退出...")
        return
    
    print("\n✅ 测试环境检查通过")
    
    # 询问是否开始测试
    print("\n📋 测试说明:")
    print("• 这将处理imgs文件夹中的所有图片")
    print("• 测试完成后会自动分析结果")
    print("• 结果将保存到output文件夹")
    
    confirm = input("\n是否开始测试? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("测试已取消")
        return
    
    # 运行测试
    success = run_quick_test()
    
    if success:
        # 分析结果
        analyze_results()
        
        # 显示建议
        show_recommendations()
        
        print("\n🎉 快速测试完成!")
    else:
        print("\n❌ 测试失败，请检查错误信息")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
