# 配置文件说明

本文档详细说明了 `config.json` 配置文件中各个参数的含义和用法。

## 📋 配置文件结构

### 1. OCR设置 (ocr_settings)

```json
{
  "ocr_settings": {
    "use_angle_cls": true,           // 是否使用角度分类器
    "lang": "ch",                    // 识别语言 (ch=中文, en=英文)
    "use_gpu": false,                // 是否使用GPU加速
    "confidence_threshold": 0.5      // 置信度阈值 (0.0-1.0)
  }
}
```

**参数说明：**
- `use_angle_cls`: 启用角度分类器可以处理旋转的文字，但会增加处理时间
- `lang`: 设置识别语言，中文成绩单建议使用 "ch"
- `use_gpu`: 如果有NVIDIA GPU且安装了CUDA，可设为true加速处理
- `confidence_threshold`: 只保留置信度高于此值的识别结果，提高准确性

### 2. 图片预处理 (image_preprocessing)

```json
{
  "image_preprocessing": {
    "enable_denoising": true,        // 启用去噪处理
    "enable_contrast_enhancement": true,  // 启用对比度增强
    "enable_sharpening": true,       // 启用锐化处理
    "resize_factor": 1.0,            // 图片缩放比例
    "gaussian_blur_kernel": 3        // 高斯模糊核大小
  }
}
```

**参数说明：**
- `enable_denoising`: 去除图片噪点，改善识别效果
- `enable_contrast_enhancement`: 增强对比度，使文字更清晰
- `enable_sharpening`: 锐化处理，增强边缘清晰度
- `resize_factor`: 图片缩放比例，1.0为原始大小，>1.0放大，<1.0缩小
- `gaussian_blur_kernel`: 高斯模糊核大小，0表示不使用模糊

### 3. 提取模式 (extraction_patterns)

```json
{
  "extraction_patterns": {
    "name": [
      "姓\\s*名[：:：]\\s*([^\\s\\n]+)",
      "姓名\\s*[：:：]\\s*([^\\s\\n]+)"
    ]
  }
}
```

**自定义提取模式：**
- 使用正则表达式定义字段提取规则
- 支持多个模式，按顺序尝试匹配
- 可以根据不同成绩单格式添加新的模式

**常用正则表达式符号：**
- `\\s*`: 匹配零个或多个空白字符
- `[：:：]`: 匹配中文冒号、英文冒号或全角冒号
- `([^\\s\\n]+)`: 捕获非空白、非换行的字符
- `([0-9]+)`: 捕获数字

### 4. 验证规则 (validation_rules)

```json
{
  "validation_rules": {
    "id_number_length": [15, 18],    // 身份证号长度范围
    "exam_number_length": [8, 15],   // 考生号长度范围
    "score_range": {                 // 单科分数范围
      "min": 0,
      "max": 150
    },
    "total_score_range": {           // 总分范围
      "min": 0,
      "max": 900
    }
  }
}
```

### 5. 输出设置 (output_settings)

```json
{
  "output_settings": {
    "excel_format": "xlsx",          // Excel文件格式
    "include_ocr_text": true,        // 是否包含OCR原始文本
    "include_summary": true,         // 是否包含汇总信息
    "auto_open_result": false        // 是否自动打开结果文件
  }
}
```

### 6. 处理设置 (processing_settings)

```json
{
  "processing_settings": {
    "max_workers": 4,                // 最大工作线程数
    "batch_size": 10,                // 批处理大小
    "retry_count": 2,                // 重试次数
    "timeout_seconds": 30            // 超时时间（秒）
  }
}
```

## 🔧 常用配置调整

### 提高识别准确性

```json
{
  "ocr_settings": {
    "confidence_threshold": 0.7      // 提高置信度阈值
  },
  "image_preprocessing": {
    "enable_contrast_enhancement": true,
    "enable_sharpening": true
  }
}
```

### 加速处理（适用于大量图片）

```json
{
  "processing_settings": {
    "max_workers": 8,                // 增加线程数
    "retry_count": 1                 // 减少重试次数
  },
  "image_preprocessing": {
    "enable_denoising": false,       // 关闭耗时的预处理
    "enable_sharpening": false
  }
}
```

### 处理低质量图片

```json
{
  "ocr_settings": {
    "confidence_threshold": 0.3      // 降低置信度阈值
  },
  "image_preprocessing": {
    "enable_denoising": true,
    "enable_contrast_enhancement": true,
    "resize_factor": 1.5             // 放大图片
  },
  "processing_settings": {
    "retry_count": 3                 // 增加重试次数
  }
}
```

## 📝 自定义提取模式示例

### 添加新的姓名格式

```json
{
  "extraction_patterns": {
    "name": [
      "姓\\s*名[：:：]\\s*([^\\s\\n]+)",
      "学生姓名[：:：]\\s*([^\\s\\n]+)",
      "考生[：:：]\\s*([^\\s\\n]+)"
    ]
  }
}
```

### 添加新的分数格式

```json
{
  "extraction_patterns": {
    "chinese": [
      "语\\s*文[：:：]\\s*([0-9]+)",
      "语文成绩[：:：]\\s*([0-9]+)",
      "语文\\s*([0-9]+)分"
    ]
  }
}
```

## ⚠️ 注意事项

1. **修改配置后需要重启程序**
2. **正则表达式语法错误会导致提取失败**
3. **线程数不宜设置过高，建议不超过CPU核心数的2倍**
4. **GPU加速需要安装对应的CUDA版本**
5. **置信度阈值过高可能导致漏检，过低可能产生误识别**

## 🔄 恢复默认配置

如果配置文件出现问题，可以删除 `config.json` 文件，程序会自动使用默认配置。

或者从项目中复制一份新的配置文件：

```bash
# 备份当前配置
copy config.json config_backup.json

# 删除配置文件让程序重新生成
del config.json
```
